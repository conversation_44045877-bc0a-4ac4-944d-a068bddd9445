{"name": "@types/bcrypt", "version": "6.0.0", "description": "TypeScript definitions for bcrypt", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/bcrypt", "license": "MIT", "contributors": [{"name": " <PERSON>", "githubUsername": "codeanimal", "url": "https://github.com/codeanimal"}, {"name": "<PERSON><PERSON>", "githubUsername": "IOAyman", "url": "https://github.com/IOAyman"}, {"name": "<PERSON>", "githubUsername": "dstapleton92", "url": "https://github.com/dstapleton92"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/bcrypt"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "c7ffb6808698d26d2a0773b7ab515669c7a7773a6ee8e4c90aad80972ca8ae9b", "typeScriptVersion": "5.1"}