{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/lib/generated/prisma/runtime/index-browser.js"], "sourcesContent": ["\n/* !!! This is code generated by Prisma. Do not edit directly. !!!\n/* eslint-disable */\n\"use strict\";var pe=Object.defineProperty;var Xe=Object.getOwnPropertyDescriptor;var Ke=Object.getOwnPropertyNames;var Qe=Object.prototype.hasOwnProperty;var Ye=e=>{throw TypeError(e)};var Oe=(e,n)=>{for(var i in n)pe(e,i,{get:n[i],enumerable:!0})},xe=(e,n,i,t)=>{if(n&&typeof n==\"object\"||typeof n==\"function\")for(let r of Ke(n))!Qe.call(e,r)&&r!==i&&pe(e,r,{get:()=>n[r],enumerable:!(t=Xe(n,r))||t.enumerable});return e};var ze=e=>xe(pe({},\"__esModule\",{value:!0}),e);var ne=(e,n,i)=>n.has(e)?Ye(\"Cannot add the same private member more than once\"):n instanceof WeakSet?n.add(e):n.set(e,i);var ii={};Oe(ii,{Decimal:()=>Je,Public:()=>ge,getRuntime:()=>_e,makeStrictEnum:()=>qe,objectEnumValues:()=>Ae});module.exports=ze(ii);var ge={};Oe(ge,{validator:()=>Re});function Re(...e){return n=>n}var ie=Symbol(),me=new WeakMap,we=class{constructor(n){n===ie?me.set(this,\"Prisma.\".concat(this._getName())):me.set(this,\"new Prisma.\".concat(this._getNamespace(),\".\").concat(this._getName(),\"()\"))}_getName(){return this.constructor.name}toString(){return me.get(this)}},G=class extends we{_getNamespace(){return\"NullTypes\"}},Ne,J=class extends G{constructor(){super(...arguments);ne(this,Ne)}};Ne=new WeakMap;ke(J,\"DbNull\");var ve,X=class extends G{constructor(){super(...arguments);ne(this,ve)}};ve=new WeakMap;ke(X,\"JsonNull\");var Ee,K=class extends G{constructor(){super(...arguments);ne(this,Ee)}};Ee=new WeakMap;ke(K,\"AnyNull\");var Ae={classes:{DbNull:J,JsonNull:X,AnyNull:K},instances:{DbNull:new J(ie),JsonNull:new X(ie),AnyNull:new K(ie)}};function ke(e,n){Object.defineProperty(e,\"name\",{value:n,configurable:!0})}var ye=new Set([\"toJSON\",\"$$typeof\",\"asymmetricMatch\",Symbol.iterator,Symbol.toStringTag,Symbol.isConcatSpreadable,Symbol.toPrimitive]);function qe(e){return new Proxy(e,{get(n,i){if(i in n)return n[i];if(!ye.has(i))throw new TypeError(\"Invalid enum value: \".concat(String(i)))}})}var en=()=>{var e,n;return((n=(e=globalThis.process)==null?void 0:e.release)==null?void 0:n.name)===\"node\"},nn=()=>{var e,n;return!!globalThis.Bun||!!((n=(e=globalThis.process)==null?void 0:e.versions)!=null&&n.bun)},tn=()=>!!globalThis.Deno,rn=()=>typeof globalThis.Netlify==\"object\",sn=()=>typeof globalThis.EdgeRuntime==\"object\",on=()=>{var e;return((e=globalThis.navigator)==null?void 0:e.userAgent)===\"Cloudflare-Workers\"};function un(){var i;return(i=[[rn,\"netlify\"],[sn,\"edge-light\"],[on,\"workerd\"],[tn,\"deno\"],[nn,\"bun\"],[en,\"node\"]].flatMap(t=>t[0]()?[t[1]]:[]).at(0))!=null?i:\"\"}var fn={node:\"Node.js\",workerd:\"Cloudflare Workers\",deno:\"Deno and Deno Deploy\",netlify:\"Netlify Edge Functions\",\"edge-light\":\"Edge Runtime (Vercel Edge Functions, Vercel Edge Middleware, Next.js (Pages Router) Edge API Routes, Next.js (App Router) Edge Route Handlers or Next.js Middleware)\"};function _e(){let e=un();return{id:e,prettyName:fn[e]||e,isEdge:[\"workerd\",\"deno\",\"netlify\",\"edge-light\"].includes(e)}}var V=9e15,H=1e9,Se=\"0123456789abcdef\",se=\"2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058\",oe=\"3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789\",Me={precision:20,rounding:4,modulo:1,toExpNeg:-7,toExpPos:21,minE:-V,maxE:V,crypto:!1},Le,Z,w=!0,fe=\"[DecimalError] \",$=fe+\"Invalid argument: \",Ie=fe+\"Precision limit exceeded\",Ze=fe+\"crypto unavailable\",Ue=\"[object Decimal]\",R=Math.floor,C=Math.pow,cn=/^0b([01]+(\\.[01]*)?|\\.[01]+)(p[+-]?\\d+)?$/i,ln=/^0x([0-9a-f]+(\\.[0-9a-f]*)?|\\.[0-9a-f]+)(p[+-]?\\d+)?$/i,an=/^0o([0-7]+(\\.[0-7]*)?|\\.[0-7]+)(p[+-]?\\d+)?$/i,Be=/^(\\d+(\\.\\d*)?|\\.\\d+)(e[+-]?\\d+)?$/i,D=1e7,m=7,dn=9007199254740991,hn=se.length-1,Ce=oe.length-1,h={toStringTag:Ue};h.absoluteValue=h.abs=function(){var e=new this.constructor(this);return e.s<0&&(e.s=1),p(e)};h.ceil=function(){return p(new this.constructor(this),this.e+1,2)};h.clampedTo=h.clamp=function(e,n){var i,t=this,r=t.constructor;if(e=new r(e),n=new r(n),!e.s||!n.s)return new r(NaN);if(e.gt(n))throw Error($+n);return i=t.cmp(e),i<0?e:t.cmp(n)>0?n:new r(t)};h.comparedTo=h.cmp=function(e){var n,i,t,r,s=this,o=s.d,u=(e=new s.constructor(e)).d,c=s.s,f=e.s;if(!o||!u)return!c||!f?NaN:c!==f?c:o===u?0:!o^c<0?1:-1;if(!o[0]||!u[0])return o[0]?c:u[0]?-f:0;if(c!==f)return c;if(s.e!==e.e)return s.e>e.e^c<0?1:-1;for(t=o.length,r=u.length,n=0,i=t<r?t:r;n<i;++n)if(o[n]!==u[n])return o[n]>u[n]^c<0?1:-1;return t===r?0:t>r^c<0?1:-1};h.cosine=h.cos=function(){var e,n,i=this,t=i.constructor;return i.d?i.d[0]?(e=t.precision,n=t.rounding,t.precision=e+Math.max(i.e,i.sd())+m,t.rounding=1,i=pn(t,We(t,i)),t.precision=e,t.rounding=n,p(Z==2||Z==3?i.neg():i,e,n,!0)):new t(1):new t(NaN)};h.cubeRoot=h.cbrt=function(){var e,n,i,t,r,s,o,u,c,f,l=this,a=l.constructor;if(!l.isFinite()||l.isZero())return new a(l);for(w=!1,s=l.s*C(l.s*l,1/3),!s||Math.abs(s)==1/0?(i=b(l.d),e=l.e,(s=(e-i.length+1)%3)&&(i+=s==1||s==-2?\"0\":\"00\"),s=C(i,1/3),e=R((e+1)/3)-(e%3==(e<0?-1:2)),s==1/0?i=\"5e\"+e:(i=s.toExponential(),i=i.slice(0,i.indexOf(\"e\")+1)+e),t=new a(i),t.s=l.s):t=new a(s.toString()),o=(e=a.precision)+3;;)if(u=t,c=u.times(u).times(u),f=c.plus(l),t=k(f.plus(l).times(u),f.plus(c),o+2,1),b(u.d).slice(0,o)===(i=b(t.d)).slice(0,o))if(i=i.slice(o-3,o+1),i==\"9999\"||!r&&i==\"4999\"){if(!r&&(p(u,e+1,0),u.times(u).times(u).eq(l))){t=u;break}o+=4,r=1}else{(!+i||!+i.slice(1)&&i.charAt(0)==\"5\")&&(p(t,e+1,1),n=!t.times(t).times(t).eq(l));break}return w=!0,p(t,e,a.rounding,n)};h.decimalPlaces=h.dp=function(){var e,n=this.d,i=NaN;if(n){if(e=n.length-1,i=(e-R(this.e/m))*m,e=n[e],e)for(;e%10==0;e/=10)i--;i<0&&(i=0)}return i};h.dividedBy=h.div=function(e){return k(this,new this.constructor(e))};h.dividedToIntegerBy=h.divToInt=function(e){var n=this,i=n.constructor;return p(k(n,new i(e),0,1,1),i.precision,i.rounding)};h.equals=h.eq=function(e){return this.cmp(e)===0};h.floor=function(){return p(new this.constructor(this),this.e+1,3)};h.greaterThan=h.gt=function(e){return this.cmp(e)>0};h.greaterThanOrEqualTo=h.gte=function(e){var n=this.cmp(e);return n==1||n===0};h.hyperbolicCosine=h.cosh=function(){var e,n,i,t,r,s=this,o=s.constructor,u=new o(1);if(!s.isFinite())return new o(s.s?1/0:NaN);if(s.isZero())return u;i=o.precision,t=o.rounding,o.precision=i+Math.max(s.e,s.sd())+4,o.rounding=1,r=s.d.length,r<32?(e=Math.ceil(r/3),n=(1/le(4,e)).toString()):(e=16,n=\"2.3283064365386962890625e-10\"),s=j(o,1,s.times(n),new o(1),!0);for(var c,f=e,l=new o(8);f--;)c=s.times(s),s=u.minus(c.times(l.minus(c.times(l))));return p(s,o.precision=i,o.rounding=t,!0)};h.hyperbolicSine=h.sinh=function(){var e,n,i,t,r=this,s=r.constructor;if(!r.isFinite()||r.isZero())return new s(r);if(n=s.precision,i=s.rounding,s.precision=n+Math.max(r.e,r.sd())+4,s.rounding=1,t=r.d.length,t<3)r=j(s,2,r,r,!0);else{e=1.4*Math.sqrt(t),e=e>16?16:e|0,r=r.times(1/le(5,e)),r=j(s,2,r,r,!0);for(var o,u=new s(5),c=new s(16),f=new s(20);e--;)o=r.times(r),r=r.times(u.plus(o.times(c.times(o).plus(f))))}return s.precision=n,s.rounding=i,p(r,n,i,!0)};h.hyperbolicTangent=h.tanh=function(){var e,n,i=this,t=i.constructor;return i.isFinite()?i.isZero()?new t(i):(e=t.precision,n=t.rounding,t.precision=e+7,t.rounding=1,k(i.sinh(),i.cosh(),t.precision=e,t.rounding=n)):new t(i.s)};h.inverseCosine=h.acos=function(){var e=this,n=e.constructor,i=e.abs().cmp(1),t=n.precision,r=n.rounding;return i!==-1?i===0?e.isNeg()?F(n,t,r):new n(0):new n(NaN):e.isZero()?F(n,t+4,r).times(.5):(n.precision=t+6,n.rounding=1,e=new n(1).minus(e).div(e.plus(1)).sqrt().atan(),n.precision=t,n.rounding=r,e.times(2))};h.inverseHyperbolicCosine=h.acosh=function(){var e,n,i=this,t=i.constructor;return i.lte(1)?new t(i.eq(1)?0:NaN):i.isFinite()?(e=t.precision,n=t.rounding,t.precision=e+Math.max(Math.abs(i.e),i.sd())+4,t.rounding=1,w=!1,i=i.times(i).minus(1).sqrt().plus(i),w=!0,t.precision=e,t.rounding=n,i.ln()):new t(i)};h.inverseHyperbolicSine=h.asinh=function(){var e,n,i=this,t=i.constructor;return!i.isFinite()||i.isZero()?new t(i):(e=t.precision,n=t.rounding,t.precision=e+2*Math.max(Math.abs(i.e),i.sd())+6,t.rounding=1,w=!1,i=i.times(i).plus(1).sqrt().plus(i),w=!0,t.precision=e,t.rounding=n,i.ln())};h.inverseHyperbolicTangent=h.atanh=function(){var e,n,i,t,r=this,s=r.constructor;return r.isFinite()?r.e>=0?new s(r.abs().eq(1)?r.s/0:r.isZero()?r:NaN):(e=s.precision,n=s.rounding,t=r.sd(),Math.max(t,e)<2*-r.e-1?p(new s(r),e,n,!0):(s.precision=i=t-r.e,r=k(r.plus(1),new s(1).minus(r),i+e,1),s.precision=e+4,s.rounding=1,r=r.ln(),s.precision=e,s.rounding=n,r.times(.5))):new s(NaN)};h.inverseSine=h.asin=function(){var e,n,i,t,r=this,s=r.constructor;return r.isZero()?new s(r):(n=r.abs().cmp(1),i=s.precision,t=s.rounding,n!==-1?n===0?(e=F(s,i+4,t).times(.5),e.s=r.s,e):new s(NaN):(s.precision=i+6,s.rounding=1,r=r.div(new s(1).minus(r.times(r)).sqrt().plus(1)).atan(),s.precision=i,s.rounding=t,r.times(2)))};h.inverseTangent=h.atan=function(){var e,n,i,t,r,s,o,u,c,f=this,l=f.constructor,a=l.precision,d=l.rounding;if(f.isFinite()){if(f.isZero())return new l(f);if(f.abs().eq(1)&&a+4<=Ce)return o=F(l,a+4,d).times(.25),o.s=f.s,o}else{if(!f.s)return new l(NaN);if(a+4<=Ce)return o=F(l,a+4,d).times(.5),o.s=f.s,o}for(l.precision=u=a+10,l.rounding=1,i=Math.min(28,u/m+2|0),e=i;e;--e)f=f.div(f.times(f).plus(1).sqrt().plus(1));for(w=!1,n=Math.ceil(u/m),t=1,c=f.times(f),o=new l(f),r=f;e!==-1;)if(r=r.times(c),s=o.minus(r.div(t+=2)),r=r.times(c),o=s.plus(r.div(t+=2)),o.d[n]!==void 0)for(e=n;o.d[e]===s.d[e]&&e--;);return i&&(o=o.times(2<<i-1)),w=!0,p(o,l.precision=a,l.rounding=d,!0)};h.isFinite=function(){return!!this.d};h.isInteger=h.isInt=function(){return!!this.d&&R(this.e/m)>this.d.length-2};h.isNaN=function(){return!this.s};h.isNegative=h.isNeg=function(){return this.s<0};h.isPositive=h.isPos=function(){return this.s>0};h.isZero=function(){return!!this.d&&this.d[0]===0};h.lessThan=h.lt=function(e){return this.cmp(e)<0};h.lessThanOrEqualTo=h.lte=function(e){return this.cmp(e)<1};h.logarithm=h.log=function(e){var n,i,t,r,s,o,u,c,f=this,l=f.constructor,a=l.precision,d=l.rounding,g=5;if(e==null)e=new l(10),n=!0;else{if(e=new l(e),i=e.d,e.s<0||!i||!i[0]||e.eq(1))return new l(NaN);n=e.eq(10)}if(i=f.d,f.s<0||!i||!i[0]||f.eq(1))return new l(i&&!i[0]?-1/0:f.s!=1?NaN:i?0:1/0);if(n)if(i.length>1)s=!0;else{for(r=i[0];r%10===0;)r/=10;s=r!==1}if(w=!1,u=a+g,o=B(f,u),t=n?ue(l,u+10):B(e,u),c=k(o,t,u,1),Q(c.d,r=a,d))do if(u+=10,o=B(f,u),t=n?ue(l,u+10):B(e,u),c=k(o,t,u,1),!s){+b(c.d).slice(r+1,r+15)+1==1e14&&(c=p(c,a+1,0));break}while(Q(c.d,r+=10,d));return w=!0,p(c,a,d)};h.minus=h.sub=function(e){var n,i,t,r,s,o,u,c,f,l,a,d,g=this,v=g.constructor;if(e=new v(e),!g.d||!e.d)return!g.s||!e.s?e=new v(NaN):g.d?e.s=-e.s:e=new v(e.d||g.s!==e.s?g:NaN),e;if(g.s!=e.s)return e.s=-e.s,g.plus(e);if(f=g.d,d=e.d,u=v.precision,c=v.rounding,!f[0]||!d[0]){if(d[0])e.s=-e.s;else if(f[0])e=new v(g);else return new v(c===3?-0:0);return w?p(e,u,c):e}if(i=R(e.e/m),l=R(g.e/m),f=f.slice(),s=l-i,s){for(a=s<0,a?(n=f,s=-s,o=d.length):(n=d,i=l,o=f.length),t=Math.max(Math.ceil(u/m),o)+2,s>t&&(s=t,n.length=1),n.reverse(),t=s;t--;)n.push(0);n.reverse()}else{for(t=f.length,o=d.length,a=t<o,a&&(o=t),t=0;t<o;t++)if(f[t]!=d[t]){a=f[t]<d[t];break}s=0}for(a&&(n=f,f=d,d=n,e.s=-e.s),o=f.length,t=d.length-o;t>0;--t)f[o++]=0;for(t=d.length;t>s;){if(f[--t]<d[t]){for(r=t;r&&f[--r]===0;)f[r]=D-1;--f[r],f[t]+=D}f[t]-=d[t]}for(;f[--o]===0;)f.pop();for(;f[0]===0;f.shift())--i;return f[0]?(e.d=f,e.e=ce(f,i),w?p(e,u,c):e):new v(c===3?-0:0)};h.modulo=h.mod=function(e){var n,i=this,t=i.constructor;return e=new t(e),!i.d||!e.s||e.d&&!e.d[0]?new t(NaN):!e.d||i.d&&!i.d[0]?p(new t(i),t.precision,t.rounding):(w=!1,t.modulo==9?(n=k(i,e.abs(),0,3,1),n.s*=e.s):n=k(i,e,0,t.modulo,1),n=n.times(e),w=!0,i.minus(n))};h.naturalExponential=h.exp=function(){return be(this)};h.naturalLogarithm=h.ln=function(){return B(this)};h.negated=h.neg=function(){var e=new this.constructor(this);return e.s=-e.s,p(e)};h.plus=h.add=function(e){var n,i,t,r,s,o,u,c,f,l,a=this,d=a.constructor;if(e=new d(e),!a.d||!e.d)return!a.s||!e.s?e=new d(NaN):a.d||(e=new d(e.d||a.s===e.s?a:NaN)),e;if(a.s!=e.s)return e.s=-e.s,a.minus(e);if(f=a.d,l=e.d,u=d.precision,c=d.rounding,!f[0]||!l[0])return l[0]||(e=new d(a)),w?p(e,u,c):e;if(s=R(a.e/m),t=R(e.e/m),f=f.slice(),r=s-t,r){for(r<0?(i=f,r=-r,o=l.length):(i=l,t=s,o=f.length),s=Math.ceil(u/m),o=s>o?s+1:o+1,r>o&&(r=o,i.length=1),i.reverse();r--;)i.push(0);i.reverse()}for(o=f.length,r=l.length,o-r<0&&(r=o,i=l,l=f,f=i),n=0;r;)n=(f[--r]=f[r]+l[r]+n)/D|0,f[r]%=D;for(n&&(f.unshift(n),++t),o=f.length;f[--o]==0;)f.pop();return e.d=f,e.e=ce(f,t),w?p(e,u,c):e};h.precision=h.sd=function(e){var n,i=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error($+e);return i.d?(n=$e(i.d),e&&i.e+1>n&&(n=i.e+1)):n=NaN,n};h.round=function(){var e=this,n=e.constructor;return p(new n(e),e.e+1,n.rounding)};h.sine=h.sin=function(){var e,n,i=this,t=i.constructor;return i.isFinite()?i.isZero()?new t(i):(e=t.precision,n=t.rounding,t.precision=e+Math.max(i.e,i.sd())+m,t.rounding=1,i=mn(t,We(t,i)),t.precision=e,t.rounding=n,p(Z>2?i.neg():i,e,n,!0)):new t(NaN)};h.squareRoot=h.sqrt=function(){var e,n,i,t,r,s,o=this,u=o.d,c=o.e,f=o.s,l=o.constructor;if(f!==1||!u||!u[0])return new l(!f||f<0&&(!u||u[0])?NaN:u?o:1/0);for(w=!1,f=Math.sqrt(+o),f==0||f==1/0?(n=b(u),(n.length+c)%2==0&&(n+=\"0\"),f=Math.sqrt(n),c=R((c+1)/2)-(c<0||c%2),f==1/0?n=\"5e\"+c:(n=f.toExponential(),n=n.slice(0,n.indexOf(\"e\")+1)+c),t=new l(n)):t=new l(f.toString()),i=(c=l.precision)+3;;)if(s=t,t=s.plus(k(o,s,i+2,1)).times(.5),b(s.d).slice(0,i)===(n=b(t.d)).slice(0,i))if(n=n.slice(i-3,i+1),n==\"9999\"||!r&&n==\"4999\"){if(!r&&(p(s,c+1,0),s.times(s).eq(o))){t=s;break}i+=4,r=1}else{(!+n||!+n.slice(1)&&n.charAt(0)==\"5\")&&(p(t,c+1,1),e=!t.times(t).eq(o));break}return w=!0,p(t,c,l.rounding,e)};h.tangent=h.tan=function(){var e,n,i=this,t=i.constructor;return i.isFinite()?i.isZero()?new t(i):(e=t.precision,n=t.rounding,t.precision=e+10,t.rounding=1,i=i.sin(),i.s=1,i=k(i,new t(1).minus(i.times(i)).sqrt(),e+10,0),t.precision=e,t.rounding=n,p(Z==2||Z==4?i.neg():i,e,n,!0)):new t(NaN)};h.times=h.mul=function(e){var n,i,t,r,s,o,u,c,f,l=this,a=l.constructor,d=l.d,g=(e=new a(e)).d;if(e.s*=l.s,!d||!d[0]||!g||!g[0])return new a(!e.s||d&&!d[0]&&!g||g&&!g[0]&&!d?NaN:!d||!g?e.s/0:e.s*0);for(i=R(l.e/m)+R(e.e/m),c=d.length,f=g.length,c<f&&(s=d,d=g,g=s,o=c,c=f,f=o),s=[],o=c+f,t=o;t--;)s.push(0);for(t=f;--t>=0;){for(n=0,r=c+t;r>t;)u=s[r]+g[t]*d[r-t-1]+n,s[r--]=u%D|0,n=u/D|0;s[r]=(s[r]+n)%D|0}for(;!s[--o];)s.pop();return n?++i:s.shift(),e.d=s,e.e=ce(s,i),w?p(e,a.precision,a.rounding):e};h.toBinary=function(e,n){return Pe(this,2,e,n)};h.toDecimalPlaces=h.toDP=function(e,n){var i=this,t=i.constructor;return i=new t(i),e===void 0?i:(q(e,0,H),n===void 0?n=t.rounding:q(n,0,8),p(i,e+i.e+1,n))};h.toExponential=function(e,n){var i,t=this,r=t.constructor;return e===void 0?i=L(t,!0):(q(e,0,H),n===void 0?n=r.rounding:q(n,0,8),t=p(new r(t),e+1,n),i=L(t,!0,e+1)),t.isNeg()&&!t.isZero()?\"-\"+i:i};h.toFixed=function(e,n){var i,t,r=this,s=r.constructor;return e===void 0?i=L(r):(q(e,0,H),n===void 0?n=s.rounding:q(n,0,8),t=p(new s(r),e+r.e+1,n),i=L(t,!1,e+t.e+1)),r.isNeg()&&!r.isZero()?\"-\"+i:i};h.toFraction=function(e){var n,i,t,r,s,o,u,c,f,l,a,d,g=this,v=g.d,N=g.constructor;if(!v)return new N(g);if(f=i=new N(1),t=c=new N(0),n=new N(t),s=n.e=$e(v)-g.e-1,o=s%m,n.d[0]=C(10,o<0?m+o:o),e==null)e=s>0?n:f;else{if(u=new N(e),!u.isInt()||u.lt(f))throw Error($+u);e=u.gt(n)?s>0?n:f:u}for(w=!1,u=new N(b(v)),l=N.precision,N.precision=s=v.length*m*2;a=k(u,n,0,1,1),r=i.plus(a.times(t)),r.cmp(e)!=1;)i=t,t=r,r=f,f=c.plus(a.times(r)),c=r,r=n,n=u.minus(a.times(r)),u=r;return r=k(e.minus(i),t,0,1,1),c=c.plus(r.times(f)),i=i.plus(r.times(t)),c.s=f.s=g.s,d=k(f,t,s,1).minus(g).abs().cmp(k(c,i,s,1).minus(g).abs())<1?[f,t]:[c,i],N.precision=l,w=!0,d};h.toHexadecimal=h.toHex=function(e,n){return Pe(this,16,e,n)};h.toNearest=function(e,n){var i=this,t=i.constructor;if(i=new t(i),e==null){if(!i.d)return i;e=new t(1),n=t.rounding}else{if(e=new t(e),n===void 0?n=t.rounding:q(n,0,8),!i.d)return e.s?i:e;if(!e.d)return e.s&&(e.s=i.s),e}return e.d[0]?(w=!1,i=k(i,e,0,n,1).times(e),w=!0,p(i)):(e.s=i.s,i=e),i};h.toNumber=function(){return+this};h.toOctal=function(e,n){return Pe(this,8,e,n)};h.toPower=h.pow=function(e){var n,i,t,r,s,o,u=this,c=u.constructor,f=+(e=new c(e));if(!u.d||!e.d||!u.d[0]||!e.d[0])return new c(C(+u,f));if(u=new c(u),u.eq(1))return u;if(t=c.precision,s=c.rounding,e.eq(1))return p(u,t,s);if(n=R(e.e/m),n>=e.d.length-1&&(i=f<0?-f:f)<=dn)return r=He(c,u,i,t),e.s<0?new c(1).div(r):p(r,t,s);if(o=u.s,o<0){if(n<e.d.length-1)return new c(NaN);if((e.d[n]&1)==0&&(o=1),u.e==0&&u.d[0]==1&&u.d.length==1)return u.s=o,u}return i=C(+u,f),n=i==0||!isFinite(i)?R(f*(Math.log(\"0.\"+b(u.d))/Math.LN10+u.e+1)):new c(i+\"\").e,n>c.maxE+1||n<c.minE-1?new c(n>0?o/0:0):(w=!1,c.rounding=u.s=1,i=Math.min(12,(n+\"\").length),r=be(e.times(B(u,t+i)),t),r.d&&(r=p(r,t+5,1),Q(r.d,t,s)&&(n=t+10,r=p(be(e.times(B(u,n+i)),n),n+5,1),+b(r.d).slice(t+1,t+15)+1==1e14&&(r=p(r,t+1,0)))),r.s=o,w=!0,c.rounding=s,p(r,t,s))};h.toPrecision=function(e,n){var i,t=this,r=t.constructor;return e===void 0?i=L(t,t.e<=r.toExpNeg||t.e>=r.toExpPos):(q(e,1,H),n===void 0?n=r.rounding:q(n,0,8),t=p(new r(t),e,n),i=L(t,e<=t.e||t.e<=r.toExpNeg,e)),t.isNeg()&&!t.isZero()?\"-\"+i:i};h.toSignificantDigits=h.toSD=function(e,n){var i=this,t=i.constructor;return e===void 0?(e=t.precision,n=t.rounding):(q(e,1,H),n===void 0?n=t.rounding:q(n,0,8)),p(new t(i),e,n)};h.toString=function(){var e=this,n=e.constructor,i=L(e,e.e<=n.toExpNeg||e.e>=n.toExpPos);return e.isNeg()&&!e.isZero()?\"-\"+i:i};h.truncated=h.trunc=function(){return p(new this.constructor(this),this.e+1,1)};h.valueOf=h.toJSON=function(){var e=this,n=e.constructor,i=L(e,e.e<=n.toExpNeg||e.e>=n.toExpPos);return e.isNeg()?\"-\"+i:i};function b(e){var n,i,t,r=e.length-1,s=\"\",o=e[0];if(r>0){for(s+=o,n=1;n<r;n++)t=e[n]+\"\",i=m-t.length,i&&(s+=U(i)),s+=t;o=e[n],t=o+\"\",i=m-t.length,i&&(s+=U(i))}else if(o===0)return\"0\";for(;o%10===0;)o/=10;return s+o}function q(e,n,i){if(e!==~~e||e<n||e>i)throw Error($+e)}function Q(e,n,i,t){var r,s,o,u;for(s=e[0];s>=10;s/=10)--n;return--n<0?(n+=m,r=0):(r=Math.ceil((n+1)/m),n%=m),s=C(10,m-n),u=e[r]%s|0,t==null?n<3?(n==0?u=u/100|0:n==1&&(u=u/10|0),o=i<4&&u==99999||i>3&&u==49999||u==5e4||u==0):o=(i<4&&u+1==s||i>3&&u+1==s/2)&&(e[r+1]/s/100|0)==C(10,n-2)-1||(u==s/2||u==0)&&(e[r+1]/s/100|0)==0:n<4?(n==0?u=u/1e3|0:n==1?u=u/100|0:n==2&&(u=u/10|0),o=(t||i<4)&&u==9999||!t&&i>3&&u==4999):o=((t||i<4)&&u+1==s||!t&&i>3&&u+1==s/2)&&(e[r+1]/s/1e3|0)==C(10,n-3)-1,o}function te(e,n,i){for(var t,r=[0],s,o=0,u=e.length;o<u;){for(s=r.length;s--;)r[s]*=n;for(r[0]+=Se.indexOf(e.charAt(o++)),t=0;t<r.length;t++)r[t]>i-1&&(r[t+1]===void 0&&(r[t+1]=0),r[t+1]+=r[t]/i|0,r[t]%=i)}return r.reverse()}function pn(e,n){var i,t,r;if(n.isZero())return n;t=n.d.length,t<32?(i=Math.ceil(t/3),r=(1/le(4,i)).toString()):(i=16,r=\"2.3283064365386962890625e-10\"),e.precision+=i,n=j(e,1,n.times(r),new e(1));for(var s=i;s--;){var o=n.times(n);n=o.times(o).minus(o).times(8).plus(1)}return e.precision-=i,n}var k=function(){function e(t,r,s){var o,u=0,c=t.length;for(t=t.slice();c--;)o=t[c]*r+u,t[c]=o%s|0,u=o/s|0;return u&&t.unshift(u),t}function n(t,r,s,o){var u,c;if(s!=o)c=s>o?1:-1;else for(u=c=0;u<s;u++)if(t[u]!=r[u]){c=t[u]>r[u]?1:-1;break}return c}function i(t,r,s,o){for(var u=0;s--;)t[s]-=u,u=t[s]<r[s]?1:0,t[s]=u*o+t[s]-r[s];for(;!t[0]&&t.length>1;)t.shift()}return function(t,r,s,o,u,c){var f,l,a,d,g,v,N,A,M,_,E,P,x,I,ae,z,W,de,T,y,ee=t.constructor,he=t.s==r.s?1:-1,O=t.d,S=r.d;if(!O||!O[0]||!S||!S[0])return new ee(!t.s||!r.s||(O?S&&O[0]==S[0]:!S)?NaN:O&&O[0]==0||!S?he*0:he/0);for(c?(g=1,l=t.e-r.e):(c=D,g=m,l=R(t.e/g)-R(r.e/g)),T=S.length,W=O.length,M=new ee(he),_=M.d=[],a=0;S[a]==(O[a]||0);a++);if(S[a]>(O[a]||0)&&l--,s==null?(I=s=ee.precision,o=ee.rounding):u?I=s+(t.e-r.e)+1:I=s,I<0)_.push(1),v=!0;else{if(I=I/g+2|0,a=0,T==1){for(d=0,S=S[0],I++;(a<W||d)&&I--;a++)ae=d*c+(O[a]||0),_[a]=ae/S|0,d=ae%S|0;v=d||a<W}else{for(d=c/(S[0]+1)|0,d>1&&(S=e(S,d,c),O=e(O,d,c),T=S.length,W=O.length),z=T,E=O.slice(0,T),P=E.length;P<T;)E[P++]=0;y=S.slice(),y.unshift(0),de=S[0],S[1]>=c/2&&++de;do d=0,f=n(S,E,T,P),f<0?(x=E[0],T!=P&&(x=x*c+(E[1]||0)),d=x/de|0,d>1?(d>=c&&(d=c-1),N=e(S,d,c),A=N.length,P=E.length,f=n(N,E,A,P),f==1&&(d--,i(N,T<A?y:S,A,c))):(d==0&&(f=d=1),N=S.slice()),A=N.length,A<P&&N.unshift(0),i(E,N,P,c),f==-1&&(P=E.length,f=n(S,E,T,P),f<1&&(d++,i(E,T<P?y:S,P,c))),P=E.length):f===0&&(d++,E=[0]),_[a++]=d,f&&E[0]?E[P++]=O[z]||0:(E=[O[z]],P=1);while((z++<W||E[0]!==void 0)&&I--);v=E[0]!==void 0}_[0]||_.shift()}if(g==1)M.e=l,Le=v;else{for(a=1,d=_[0];d>=10;d/=10)a++;M.e=a+l*g-1,p(M,u?s+M.e+1:s,o,v)}return M}}();function p(e,n,i,t){var r,s,o,u,c,f,l,a,d,g=e.constructor;e:if(n!=null){if(a=e.d,!a)return e;for(r=1,u=a[0];u>=10;u/=10)r++;if(s=n-r,s<0)s+=m,o=n,l=a[d=0],c=l/C(10,r-o-1)%10|0;else if(d=Math.ceil((s+1)/m),u=a.length,d>=u)if(t){for(;u++<=d;)a.push(0);l=c=0,r=1,s%=m,o=s-m+1}else break e;else{for(l=u=a[d],r=1;u>=10;u/=10)r++;s%=m,o=s-m+r,c=o<0?0:l/C(10,r-o-1)%10|0}if(t=t||n<0||a[d+1]!==void 0||(o<0?l:l%C(10,r-o-1)),f=i<4?(c||t)&&(i==0||i==(e.s<0?3:2)):c>5||c==5&&(i==4||t||i==6&&(s>0?o>0?l/C(10,r-o):0:a[d-1])%10&1||i==(e.s<0?8:7)),n<1||!a[0])return a.length=0,f?(n-=e.e+1,a[0]=C(10,(m-n%m)%m),e.e=-n||0):a[0]=e.e=0,e;if(s==0?(a.length=d,u=1,d--):(a.length=d+1,u=C(10,m-s),a[d]=o>0?(l/C(10,r-o)%C(10,o)|0)*u:0),f)for(;;)if(d==0){for(s=1,o=a[0];o>=10;o/=10)s++;for(o=a[0]+=u,u=1;o>=10;o/=10)u++;s!=u&&(e.e++,a[0]==D&&(a[0]=1));break}else{if(a[d]+=u,a[d]!=D)break;a[d--]=0,u=1}for(s=a.length;a[--s]===0;)a.pop()}return w&&(e.e>g.maxE?(e.d=null,e.e=NaN):e.e<g.minE&&(e.e=0,e.d=[0])),e}function L(e,n,i){if(!e.isFinite())return je(e);var t,r=e.e,s=b(e.d),o=s.length;return n?(i&&(t=i-o)>0?s=s.charAt(0)+\".\"+s.slice(1)+U(t):o>1&&(s=s.charAt(0)+\".\"+s.slice(1)),s=s+(e.e<0?\"e\":\"e+\")+e.e):r<0?(s=\"0.\"+U(-r-1)+s,i&&(t=i-o)>0&&(s+=U(t))):r>=o?(s+=U(r+1-o),i&&(t=i-r-1)>0&&(s=s+\".\"+U(t))):((t=r+1)<o&&(s=s.slice(0,t)+\".\"+s.slice(t)),i&&(t=i-o)>0&&(r+1===o&&(s+=\".\"),s+=U(t))),s}function ce(e,n){var i=e[0];for(n*=m;i>=10;i/=10)n++;return n}function ue(e,n,i){if(n>hn)throw w=!0,i&&(e.precision=i),Error(Ie);return p(new e(se),n,1,!0)}function F(e,n,i){if(n>Ce)throw Error(Ie);return p(new e(oe),n,i,!0)}function $e(e){var n=e.length-1,i=n*m+1;if(n=e[n],n){for(;n%10==0;n/=10)i--;for(n=e[0];n>=10;n/=10)i++}return i}function U(e){for(var n=\"\";e--;)n+=\"0\";return n}function He(e,n,i,t){var r,s=new e(1),o=Math.ceil(t/m+4);for(w=!1;;){if(i%2&&(s=s.times(n),De(s.d,o)&&(r=!0)),i=R(i/2),i===0){i=s.d.length-1,r&&s.d[i]===0&&++s.d[i];break}n=n.times(n),De(n.d,o)}return w=!0,s}function Te(e){return e.d[e.d.length-1]&1}function Ve(e,n,i){for(var t,r,s=new e(n[0]),o=0;++o<n.length;){if(r=new e(n[o]),!r.s){s=r;break}t=s.cmp(r),(t===i||t===0&&s.s===i)&&(s=r)}return s}function be(e,n){var i,t,r,s,o,u,c,f=0,l=0,a=0,d=e.constructor,g=d.rounding,v=d.precision;if(!e.d||!e.d[0]||e.e>17)return new d(e.d?e.d[0]?e.s<0?0:1/0:1:e.s?e.s<0?0:e:NaN);for(n==null?(w=!1,c=v):c=n,u=new d(.03125);e.e>-2;)e=e.times(u),a+=5;for(t=Math.log(C(2,a))/Math.LN10*2+5|0,c+=t,i=s=o=new d(1),d.precision=c;;){if(s=p(s.times(e),c,1),i=i.times(++l),u=o.plus(k(s,i,c,1)),b(u.d).slice(0,c)===b(o.d).slice(0,c)){for(r=a;r--;)o=p(o.times(o),c,1);if(n==null)if(f<3&&Q(o.d,c-t,g,f))d.precision=c+=10,i=s=u=new d(1),l=0,f++;else return p(o,d.precision=v,g,w=!0);else return d.precision=v,o}o=u}}function B(e,n){var i,t,r,s,o,u,c,f,l,a,d,g=1,v=10,N=e,A=N.d,M=N.constructor,_=M.rounding,E=M.precision;if(N.s<0||!A||!A[0]||!N.e&&A[0]==1&&A.length==1)return new M(A&&!A[0]?-1/0:N.s!=1?NaN:A?0:N);if(n==null?(w=!1,l=E):l=n,M.precision=l+=v,i=b(A),t=i.charAt(0),Math.abs(s=N.e)<15e14){for(;t<7&&t!=1||t==1&&i.charAt(1)>3;)N=N.times(e),i=b(N.d),t=i.charAt(0),g++;s=N.e,t>1?(N=new M(\"0.\"+i),s++):N=new M(t+\".\"+i.slice(1))}else return f=ue(M,l+2,E).times(s+\"\"),N=B(new M(t+\".\"+i.slice(1)),l-v).plus(f),M.precision=E,n==null?p(N,E,_,w=!0):N;for(a=N,c=o=N=k(N.minus(1),N.plus(1),l,1),d=p(N.times(N),l,1),r=3;;){if(o=p(o.times(d),l,1),f=c.plus(k(o,new M(r),l,1)),b(f.d).slice(0,l)===b(c.d).slice(0,l))if(c=c.times(2),s!==0&&(c=c.plus(ue(M,l+2,E).times(s+\"\"))),c=k(c,new M(g),l,1),n==null)if(Q(c.d,l-v,_,u))M.precision=l+=v,f=o=N=k(a.minus(1),a.plus(1),l,1),d=p(N.times(N),l,1),r=u=1;else return p(c,M.precision=E,_,w=!0);else return M.precision=E,c;c=f,r+=2}}function je(e){return String(e.s*e.s/0)}function re(e,n){var i,t,r;for((i=n.indexOf(\".\"))>-1&&(n=n.replace(\".\",\"\")),(t=n.search(/e/i))>0?(i<0&&(i=t),i+=+n.slice(t+1),n=n.substring(0,t)):i<0&&(i=n.length),t=0;n.charCodeAt(t)===48;t++);for(r=n.length;n.charCodeAt(r-1)===48;--r);if(n=n.slice(t,r),n){if(r-=t,e.e=i=i-t-1,e.d=[],t=(i+1)%m,i<0&&(t+=m),t<r){for(t&&e.d.push(+n.slice(0,t)),r-=m;t<r;)e.d.push(+n.slice(t,t+=m));n=n.slice(t),t=m-n.length}else t-=r;for(;t--;)n+=\"0\";e.d.push(+n),w&&(e.e>e.constructor.maxE?(e.d=null,e.e=NaN):e.e<e.constructor.minE&&(e.e=0,e.d=[0]))}else e.e=0,e.d=[0];return e}function gn(e,n){var i,t,r,s,o,u,c,f,l;if(n.indexOf(\"_\")>-1){if(n=n.replace(/(\\d)_(?=\\d)/g,\"$1\"),Be.test(n))return re(e,n)}else if(n===\"Infinity\"||n===\"NaN\")return+n||(e.s=NaN),e.e=NaN,e.d=null,e;if(ln.test(n))i=16,n=n.toLowerCase();else if(cn.test(n))i=2;else if(an.test(n))i=8;else throw Error($+n);for(s=n.search(/p/i),s>0?(c=+n.slice(s+1),n=n.substring(2,s)):n=n.slice(2),s=n.indexOf(\".\"),o=s>=0,t=e.constructor,o&&(n=n.replace(\".\",\"\"),u=n.length,s=u-s,r=He(t,new t(i),s,s*2)),f=te(n,i,D),l=f.length-1,s=l;f[s]===0;--s)f.pop();return s<0?new t(e.s*0):(e.e=ce(f,l),e.d=f,w=!1,o&&(e=k(e,r,u*4)),c&&(e=e.times(Math.abs(c)<54?C(2,c):Y.pow(2,c))),w=!0,e)}function mn(e,n){var i,t=n.d.length;if(t<3)return n.isZero()?n:j(e,2,n,n);i=1.4*Math.sqrt(t),i=i>16?16:i|0,n=n.times(1/le(5,i)),n=j(e,2,n,n);for(var r,s=new e(5),o=new e(16),u=new e(20);i--;)r=n.times(n),n=n.times(s.plus(r.times(o.times(r).minus(u))));return n}function j(e,n,i,t,r){var s,o,u,c,f=1,l=e.precision,a=Math.ceil(l/m);for(w=!1,c=i.times(i),u=new e(t);;){if(o=k(u.times(c),new e(n++*n++),l,1),u=r?t.plus(o):t.minus(o),t=k(o.times(c),new e(n++*n++),l,1),o=u.plus(t),o.d[a]!==void 0){for(s=a;o.d[s]===u.d[s]&&s--;);if(s==-1)break}s=u,u=t,t=o,o=s,f++}return w=!0,o.d.length=a+1,o}function le(e,n){for(var i=e;--n;)i*=e;return i}function We(e,n){var i,t=n.s<0,r=F(e,e.precision,1),s=r.times(.5);if(n=n.abs(),n.lte(s))return Z=t?4:1,n;if(i=n.divToInt(r),i.isZero())Z=t?3:2;else{if(n=n.minus(i.times(r)),n.lte(s))return Z=Te(i)?t?2:3:t?4:1,n;Z=Te(i)?t?1:4:t?3:2}return n.minus(r).abs()}function Pe(e,n,i,t){var r,s,o,u,c,f,l,a,d,g=e.constructor,v=i!==void 0;if(v?(q(i,1,H),t===void 0?t=g.rounding:q(t,0,8)):(i=g.precision,t=g.rounding),!e.isFinite())l=je(e);else{for(l=L(e),o=l.indexOf(\".\"),v?(r=2,n==16?i=i*4-3:n==8&&(i=i*3-2)):r=n,o>=0&&(l=l.replace(\".\",\"\"),d=new g(1),d.e=l.length-o,d.d=te(L(d),10,r),d.e=d.d.length),a=te(l,10,r),s=c=a.length;a[--c]==0;)a.pop();if(!a[0])l=v?\"0p+0\":\"0\";else{if(o<0?s--:(e=new g(e),e.d=a,e.e=s,e=k(e,d,i,t,0,r),a=e.d,s=e.e,f=Le),o=a[i],u=r/2,f=f||a[i+1]!==void 0,f=t<4?(o!==void 0||f)&&(t===0||t===(e.s<0?3:2)):o>u||o===u&&(t===4||f||t===6&&a[i-1]&1||t===(e.s<0?8:7)),a.length=i,f)for(;++a[--i]>r-1;)a[i]=0,i||(++s,a.unshift(1));for(c=a.length;!a[c-1];--c);for(o=0,l=\"\";o<c;o++)l+=Se.charAt(a[o]);if(v){if(c>1)if(n==16||n==8){for(o=n==16?4:3,--c;c%o;c++)l+=\"0\";for(a=te(l,r,n),c=a.length;!a[c-1];--c);for(o=1,l=\"1.\";o<c;o++)l+=Se.charAt(a[o])}else l=l.charAt(0)+\".\"+l.slice(1);l=l+(s<0?\"p\":\"p+\")+s}else if(s<0){for(;++s;)l=\"0\"+l;l=\"0.\"+l}else if(++s>c)for(s-=c;s--;)l+=\"0\";else s<c&&(l=l.slice(0,s)+\".\"+l.slice(s))}l=(n==16?\"0x\":n==2?\"0b\":n==8?\"0o\":\"\")+l}return e.s<0?\"-\"+l:l}function De(e,n){if(e.length>n)return e.length=n,!0}function wn(e){return new this(e).abs()}function Nn(e){return new this(e).acos()}function vn(e){return new this(e).acosh()}function En(e,n){return new this(e).plus(n)}function kn(e){return new this(e).asin()}function Sn(e){return new this(e).asinh()}function Mn(e){return new this(e).atan()}function Cn(e){return new this(e).atanh()}function bn(e,n){e=new this(e),n=new this(n);var i,t=this.precision,r=this.rounding,s=t+4;return!e.s||!n.s?i=new this(NaN):!e.d&&!n.d?(i=F(this,s,1).times(n.s>0?.25:.75),i.s=e.s):!n.d||e.isZero()?(i=n.s<0?F(this,t,r):new this(0),i.s=e.s):!e.d||n.isZero()?(i=F(this,s,1).times(.5),i.s=e.s):n.s<0?(this.precision=s,this.rounding=1,i=this.atan(k(e,n,s,1)),n=F(this,s,1),this.precision=t,this.rounding=r,i=e.s<0?i.minus(n):i.plus(n)):i=this.atan(k(e,n,s,1)),i}function Pn(e){return new this(e).cbrt()}function On(e){return p(e=new this(e),e.e+1,2)}function Rn(e,n,i){return new this(e).clamp(n,i)}function An(e){if(!e||typeof e!=\"object\")throw Error(fe+\"Object expected\");var n,i,t,r=e.defaults===!0,s=[\"precision\",1,H,\"rounding\",0,8,\"toExpNeg\",-V,0,\"toExpPos\",0,V,\"maxE\",0,V,\"minE\",-V,0,\"modulo\",0,9];for(n=0;n<s.length;n+=3)if(i=s[n],r&&(this[i]=Me[i]),(t=e[i])!==void 0)if(R(t)===t&&t>=s[n+1]&&t<=s[n+2])this[i]=t;else throw Error($+i+\": \"+t);if(i=\"crypto\",r&&(this[i]=Me[i]),(t=e[i])!==void 0)if(t===!0||t===!1||t===0||t===1)if(t)if(typeof crypto<\"u\"&&crypto&&(crypto.getRandomValues||crypto.randomBytes))this[i]=!0;else throw Error(Ze);else this[i]=!1;else throw Error($+i+\": \"+t);return this}function qn(e){return new this(e).cos()}function _n(e){return new this(e).cosh()}function Ge(e){var n,i,t;function r(s){var o,u,c,f=this;if(!(f instanceof r))return new r(s);if(f.constructor=r,Fe(s)){f.s=s.s,w?!s.d||s.e>r.maxE?(f.e=NaN,f.d=null):s.e<r.minE?(f.e=0,f.d=[0]):(f.e=s.e,f.d=s.d.slice()):(f.e=s.e,f.d=s.d?s.d.slice():s.d);return}if(c=typeof s,c===\"number\"){if(s===0){f.s=1/s<0?-1:1,f.e=0,f.d=[0];return}if(s<0?(s=-s,f.s=-1):f.s=1,s===~~s&&s<1e7){for(o=0,u=s;u>=10;u/=10)o++;w?o>r.maxE?(f.e=NaN,f.d=null):o<r.minE?(f.e=0,f.d=[0]):(f.e=o,f.d=[s]):(f.e=o,f.d=[s]);return}if(s*0!==0){s||(f.s=NaN),f.e=NaN,f.d=null;return}return re(f,s.toString())}if(c===\"string\")return(u=s.charCodeAt(0))===45?(s=s.slice(1),f.s=-1):(u===43&&(s=s.slice(1)),f.s=1),Be.test(s)?re(f,s):gn(f,s);if(c===\"bigint\")return s<0?(s=-s,f.s=-1):f.s=1,re(f,s.toString());throw Error($+s)}if(r.prototype=h,r.ROUND_UP=0,r.ROUND_DOWN=1,r.ROUND_CEIL=2,r.ROUND_FLOOR=3,r.ROUND_HALF_UP=4,r.ROUND_HALF_DOWN=5,r.ROUND_HALF_EVEN=6,r.ROUND_HALF_CEIL=7,r.ROUND_HALF_FLOOR=8,r.EUCLID=9,r.config=r.set=An,r.clone=Ge,r.isDecimal=Fe,r.abs=wn,r.acos=Nn,r.acosh=vn,r.add=En,r.asin=kn,r.asinh=Sn,r.atan=Mn,r.atanh=Cn,r.atan2=bn,r.cbrt=Pn,r.ceil=On,r.clamp=Rn,r.cos=qn,r.cosh=_n,r.div=Tn,r.exp=Dn,r.floor=Fn,r.hypot=Ln,r.ln=In,r.log=Zn,r.log10=Bn,r.log2=Un,r.max=$n,r.min=Hn,r.mod=Vn,r.mul=jn,r.pow=Wn,r.random=Gn,r.round=Jn,r.sign=Xn,r.sin=Kn,r.sinh=Qn,r.sqrt=Yn,r.sub=xn,r.sum=zn,r.tan=yn,r.tanh=ei,r.trunc=ni,e===void 0&&(e={}),e&&e.defaults!==!0)for(t=[\"precision\",\"rounding\",\"toExpNeg\",\"toExpPos\",\"maxE\",\"minE\",\"modulo\",\"crypto\"],n=0;n<t.length;)e.hasOwnProperty(i=t[n++])||(e[i]=this[i]);return r.config(e),r}function Tn(e,n){return new this(e).div(n)}function Dn(e){return new this(e).exp()}function Fn(e){return p(e=new this(e),e.e+1,3)}function Ln(){var e,n,i=new this(0);for(w=!1,e=0;e<arguments.length;)if(n=new this(arguments[e++]),n.d)i.d&&(i=i.plus(n.times(n)));else{if(n.s)return w=!0,new this(1/0);i=n}return w=!0,i.sqrt()}function Fe(e){return e instanceof Y||e&&e.toStringTag===Ue||!1}function In(e){return new this(e).ln()}function Zn(e,n){return new this(e).log(n)}function Un(e){return new this(e).log(2)}function Bn(e){return new this(e).log(10)}function $n(){return Ve(this,arguments,-1)}function Hn(){return Ve(this,arguments,1)}function Vn(e,n){return new this(e).mod(n)}function jn(e,n){return new this(e).mul(n)}function Wn(e,n){return new this(e).pow(n)}function Gn(e){var n,i,t,r,s=0,o=new this(1),u=[];if(e===void 0?e=this.precision:q(e,1,H),t=Math.ceil(e/m),this.crypto)if(crypto.getRandomValues)for(n=crypto.getRandomValues(new Uint32Array(t));s<t;)r=n[s],r>=429e7?n[s]=crypto.getRandomValues(new Uint32Array(1))[0]:u[s++]=r%1e7;else if(crypto.randomBytes){for(n=crypto.randomBytes(t*=4);s<t;)r=n[s]+(n[s+1]<<8)+(n[s+2]<<16)+((n[s+3]&127)<<24),r>=214e7?crypto.randomBytes(4).copy(n,s):(u.push(r%1e7),s+=4);s=t/4}else throw Error(Ze);else for(;s<t;)u[s++]=Math.random()*1e7|0;for(t=u[--s],e%=m,t&&e&&(r=C(10,m-e),u[s]=(t/r|0)*r);u[s]===0;s--)u.pop();if(s<0)i=0,u=[0];else{for(i=-1;u[0]===0;i-=m)u.shift();for(t=1,r=u[0];r>=10;r/=10)t++;t<m&&(i-=m-t)}return o.e=i,o.d=u,o}function Jn(e){return p(e=new this(e),e.e+1,this.rounding)}function Xn(e){return e=new this(e),e.d?e.d[0]?e.s:0*e.s:e.s||NaN}function Kn(e){return new this(e).sin()}function Qn(e){return new this(e).sinh()}function Yn(e){return new this(e).sqrt()}function xn(e,n){return new this(e).sub(n)}function zn(){var e=0,n=arguments,i=new this(n[e]);for(w=!1;i.s&&++e<n.length;)i=i.plus(n[e]);return w=!0,p(i,this.precision,this.rounding)}function yn(e){return new this(e).tan()}function ei(e){return new this(e).tanh()}function ni(e){return p(e=new this(e),e.e+1,1)}h[Symbol.for(\"nodejs.util.inspect.custom\")]=h.toString;h[Symbol.toStringTag]=\"Decimal\";var Y=h.constructor=Ge(Me);se=new Y(se);oe=new Y(oe);var Je=Y;0&&(module.exports={Decimal,Public,getRuntime,makeStrictEnum,objectEnumValues});\n/*! Bundled license information:\n\ndecimal.js/decimal.mjs:\n  (*!\n   *  decimal.js v10.5.0\n   *  An arbitrary-precision Decimal type for JavaScript.\n   *  https://github.com/MikeMcl/decimal.js\n   *  Copyright (c) 2025 Michael Mclaughlin <<EMAIL>>\n   *  MIT Licence\n   *)\n*/\n//# sourceMappingURL=index-browser.js.map\n"], "names": [], "mappings": "AACA;kBACkB,GACL,IAAI,KAAG,OAAO,cAAc;AAAC,IAAI,KAAG,OAAO,wBAAwB;AAAC,IAAI,KAAG,OAAO,mBAAmB;AAAC,IAAI,KAAG,OAAO,SAAS,CAAC,cAAc;AAAC,IAAI,KAAG,CAAA;IAAI,MAAM,UAAU;AAAE;AAAE,IAAI,KAAG,CAAC,GAAE;IAAK,IAAI,IAAI,KAAK,EAAE,GAAG,GAAE,GAAE;QAAC,KAAI,CAAC,CAAC,EAAE;QAAC,YAAW,CAAC;IAAC;AAAE,GAAE,KAAG,CAAC,GAAE,GAAE,GAAE;IAAK,IAAG,KAAG,OAAO,KAAG,YAAU,OAAO,KAAG,YAAW,KAAI,IAAI,KAAK,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,GAAE,MAAI,MAAI,KAAG,GAAG,GAAE,GAAE;QAAC,KAAI,IAAI,CAAC,CAAC,EAAE;QAAC,YAAW,CAAC,CAAC,IAAE,GAAG,GAAE,EAAE,KAAG,EAAE,UAAU;IAAA;IAAG,OAAO;AAAC;AAAE,IAAI,KAAG,CAAA,IAAG,GAAG,GAAG,CAAC,GAAE,cAAa;QAAC,OAAM,CAAC;IAAC,IAAG;AAAG,IAAI,KAAG,CAAC,GAAE,GAAE,IAAI,EAAE,GAAG,CAAC,KAAG,GAAG,uDAAqD,aAAa,UAAQ,EAAE,GAAG,CAAC,KAAG,EAAE,GAAG,CAAC,GAAE;AAAG,IAAI,KAAG,CAAC;AAAE,GAAG,IAAG;IAAC,SAAQ,IAAI;IAAG,QAAO,IAAI;IAAG,YAAW,IAAI;IAAG,gBAAe,IAAI;IAAG,kBAAiB,IAAI;AAAE;AAAG,OAAO,OAAO,GAAC,GAAG;AAAI,IAAI,KAAG,CAAC;AAAE,GAAG,IAAG;IAAC,WAAU,IAAI;AAAE;AAAG,SAAS,GAAG,GAAG,CAAC;IAAE,OAAO,CAAA,IAAG;AAAC;AAAC,IAAI,KAAG,UAAS,KAAG,IAAI,SAAQ,KAAG;IAAM,YAAY,CAAC,CAAC;QAAC,MAAI,KAAG,GAAG,GAAG,CAAC,IAAI,EAAC,UAAU,MAAM,CAAC,IAAI,CAAC,QAAQ,OAAK,GAAG,GAAG,CAAC,IAAI,EAAC,cAAc,MAAM,CAAC,IAAI,CAAC,aAAa,IAAG,KAAK,MAAM,CAAC,IAAI,CAAC,QAAQ,IAAG;IAAM;IAAC,WAAU;QAAC,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI;IAAA;IAAC,WAAU;QAAC,OAAO,GAAG,GAAG,CAAC,IAAI;IAAC;AAAC,GAAE,IAAE,cAAc;IAAG,gBAAe;QAAC,OAAM;IAAW;AAAC,GAAE,IAAG,IAAE,cAAc;IAAE,aAAa;QAAC,KAAK,IAAI;QAAW,GAAG,IAAI,EAAC;IAAG;AAAC;AAAE,KAAG,IAAI;AAAQ,GAAG,GAAE;AAAU,IAAI,IAAG,IAAE,cAAc;IAAE,aAAa;QAAC,KAAK,IAAI;QAAW,GAAG,IAAI,EAAC;IAAG;AAAC;AAAE,KAAG,IAAI;AAAQ,GAAG,GAAE;AAAY,IAAI,IAAG,IAAE,cAAc;IAAE,aAAa;QAAC,KAAK,IAAI;QAAW,GAAG,IAAI,EAAC;IAAG;AAAC;AAAE,KAAG,IAAI;AAAQ,GAAG,GAAE;AAAW,IAAI,KAAG;IAAC,SAAQ;QAAC,QAAO;QAAE,UAAS;QAAE,SAAQ;IAAC;IAAE,WAAU;QAAC,QAAO,IAAI,EAAE;QAAI,UAAS,IAAI,EAAE;QAAI,SAAQ,IAAI,EAAE;IAAG;AAAC;AAAE,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,OAAO,cAAc,CAAC,GAAE,QAAO;QAAC,OAAM;QAAE,cAAa,CAAC;IAAC;AAAE;AAAC,IAAI,KAAG,IAAI,IAAI;IAAC;IAAS;IAAW;IAAkB,OAAO,QAAQ;IAAC,OAAO,WAAW;IAAC,OAAO,kBAAkB;IAAC,OAAO,WAAW;CAAC;AAAE,SAAS,GAAG,CAAC;IAAE,OAAO,IAAI,MAAM,GAAE;QAAC,KAAI,CAAC,EAAC,CAAC;YAAE,IAAG,KAAK,GAAE,OAAO,CAAC,CAAC,EAAE;YAAC,IAAG,CAAC,GAAG,GAAG,CAAC,IAAG,MAAM,IAAI,UAAU,uBAAuB,MAAM,CAAC,OAAO;QAAI;IAAC;AAAE;AAAC,IAAI,KAAG;IAAK,IAAI,GAAE;IAAE,OAAM,CAAC,CAAC,IAAE,CAAC,IAAE,WAAW,OAAO,KAAG,OAAK,KAAK,IAAE,EAAE,OAAO,KAAG,OAAK,KAAK,IAAE,EAAE,IAAI,MAAI;AAAM,GAAE,KAAG;IAAK,IAAI,GAAE;IAAE,OAAM,CAAC,CAAC,WAAW,GAAG,IAAE,CAAC,CAAC,CAAC,CAAC,IAAE,CAAC,IAAE,WAAW,OAAO,KAAG,OAAK,KAAK,IAAE,EAAE,QAAQ,KAAG,QAAM,EAAE,GAAG;AAAC,GAAE,KAAG,IAAI,CAAC,CAAC,WAAW,IAAI,EAAC,KAAG,IAAI,OAAO,WAAW,OAAO,IAAE,UAAS,KAAG,IAAI,OAAO,WAAW,WAAW,IAAE,UAAS,KAAG;IAAK,IAAI;IAAE,OAAM,CAAC,CAAC,IAAE,WAAW,SAAS,KAAG,OAAK,KAAK,IAAE,EAAE,SAAS,MAAI;AAAoB;AAAE,SAAS;IAAK,IAAI;IAAE,OAAM,CAAC,IAAE;QAAC;YAAC;YAAG;SAAU;QAAC;YAAC;YAAG;SAAa;QAAC;YAAC;YAAG;SAAU;QAAC;YAAC;YAAG;SAAO;QAAC;YAAC;YAAG;SAAM;QAAC;YAAC;YAAG;SAAO;KAAC,CAAC,OAAO,CAAC,CAAA,IAAG,CAAC,CAAC,EAAE,KAAG;YAAC,CAAC,CAAC,EAAE;SAAC,GAAC,EAAE,EAAE,EAAE,CAAC,EAAE,KAAG,OAAK,IAAE;AAAE;AAAC,IAAI,KAAG;IAAC,MAAK;IAAU,SAAQ;IAAqB,MAAK;IAAuB,SAAQ;IAAyB,cAAa;AAAsK;AAAE,SAAS;IAAK,IAAI,IAAE;IAAK,OAAM;QAAC,IAAG;QAAE,YAAW,EAAE,CAAC,EAAE,IAAE;QAAE,QAAO;YAAC;YAAU;YAAO;YAAU;SAAa,CAAC,QAAQ,CAAC;IAAE;AAAC;AAAC,IAAI,IAAE,MAAK,IAAE,KAAI,KAAG,oBAAmB,KAAG,sgCAAqgC,KAAG,sgCAAqgC,KAAG;IAAC,WAAU;IAAG,UAAS;IAAE,QAAO;IAAE,UAAS,CAAC;IAAE,UAAS;IAAG,MAAK,CAAC;IAAE,MAAK;IAAE,QAAO,CAAC;AAAC,GAAE,IAAG,GAAE,IAAE,CAAC,GAAE,KAAG,mBAAkB,IAAE,KAAG,sBAAqB,KAAG,KAAG,4BAA2B,KAAG,KAAG,sBAAqB,KAAG,oBAAmB,IAAE,KAAK,KAAK,EAAC,IAAE,KAAK,GAAG,EAAC,KAAG,8CAA6C,KAAG,0DAAyD,KAAG,iDAAgD,KAAG,sCAAqC,IAAE,KAAI,IAAE,GAAE,KAAG,kBAAiB,KAAG,GAAG,MAAM,GAAC,GAAE,KAAG,GAAG,MAAM,GAAC,GAAE,IAAE;IAAC,aAAY;AAAE;AAAE,EAAE,aAAa,GAAC,EAAE,GAAG,GAAC;IAAW,IAAI,IAAE,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI;IAAE,OAAO,EAAE,CAAC,GAAC,KAAG,CAAC,EAAE,CAAC,GAAC,CAAC,GAAE,EAAE;AAAE;AAAE,EAAE,IAAI,GAAC;IAAW,OAAO,EAAE,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,GAAE,IAAI,CAAC,CAAC,GAAC,GAAE;AAAE;AAAE,EAAE,SAAS,GAAC,EAAE,KAAK,GAAC,SAAS,CAAC,EAAC,CAAC;IAAE,IAAI,GAAE,IAAE,IAAI,EAAC,IAAE,EAAE,WAAW;IAAC,IAAG,IAAE,IAAI,EAAE,IAAG,IAAE,IAAI,EAAE,IAAG,CAAC,EAAE,CAAC,IAAE,CAAC,EAAE,CAAC,EAAC,OAAO,IAAI,EAAE;IAAK,IAAG,EAAE,EAAE,CAAC,IAAG,MAAM,MAAM,IAAE;IAAG,OAAO,IAAE,EAAE,GAAG,CAAC,IAAG,IAAE,IAAE,IAAE,EAAE,GAAG,CAAC,KAAG,IAAE,IAAE,IAAI,EAAE;AAAE;AAAE,EAAE,UAAU,GAAC,EAAE,GAAG,GAAC,SAAS,CAAC;IAAE,IAAI,GAAE,GAAE,GAAE,GAAE,IAAE,IAAI,EAAC,IAAE,EAAE,CAAC,EAAC,IAAE,CAAC,IAAE,IAAI,EAAE,WAAW,CAAC,EAAE,EAAE,CAAC,EAAC,IAAE,EAAE,CAAC,EAAC,IAAE,EAAE,CAAC;IAAC,IAAG,CAAC,KAAG,CAAC,GAAE,OAAM,CAAC,KAAG,CAAC,IAAE,MAAI,MAAI,IAAE,IAAE,MAAI,IAAE,IAAE,CAAC,IAAE,IAAE,IAAE,IAAE,CAAC;IAAE,IAAG,CAAC,CAAC,CAAC,EAAE,IAAE,CAAC,CAAC,CAAC,EAAE,EAAC,OAAO,CAAC,CAAC,EAAE,GAAC,IAAE,CAAC,CAAC,EAAE,GAAC,CAAC,IAAE;IAAE,IAAG,MAAI,GAAE,OAAO;IAAE,IAAG,EAAE,CAAC,KAAG,EAAE,CAAC,EAAC,OAAO,EAAE,CAAC,GAAC,EAAE,CAAC,GAAC,IAAE,IAAE,IAAE,CAAC;IAAE,IAAI,IAAE,EAAE,MAAM,EAAC,IAAE,EAAE,MAAM,EAAC,IAAE,GAAE,IAAE,IAAE,IAAE,IAAE,GAAE,IAAE,GAAE,EAAE,EAAE,IAAG,CAAC,CAAC,EAAE,KAAG,CAAC,CAAC,EAAE,EAAC,OAAO,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,IAAE,IAAE,IAAE,CAAC;IAAE,OAAO,MAAI,IAAE,IAAE,IAAE,IAAE,IAAE,IAAE,IAAE,CAAC;AAAC;AAAE,EAAE,MAAM,GAAC,EAAE,GAAG,GAAC;IAAW,IAAI,GAAE,GAAE,IAAE,IAAI,EAAC,IAAE,EAAE,WAAW;IAAC,OAAO,EAAE,CAAC,GAAC,EAAE,CAAC,CAAC,EAAE,GAAC,CAAC,IAAE,EAAE,SAAS,EAAC,IAAE,EAAE,QAAQ,EAAC,EAAE,SAAS,GAAC,IAAE,KAAK,GAAG,CAAC,EAAE,CAAC,EAAC,EAAE,EAAE,MAAI,GAAE,EAAE,QAAQ,GAAC,GAAE,IAAE,GAAG,GAAE,GAAG,GAAE,KAAI,EAAE,SAAS,GAAC,GAAE,EAAE,QAAQ,GAAC,GAAE,EAAE,KAAG,KAAG,KAAG,IAAE,EAAE,GAAG,KAAG,GAAE,GAAE,GAAE,CAAC,EAAE,IAAE,IAAI,EAAE,KAAG,IAAI,EAAE;AAAI;AAAE,EAAE,QAAQ,GAAC,EAAE,IAAI,GAAC;IAAW,IAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAE,IAAI,EAAC,IAAE,EAAE,WAAW;IAAC,IAAG,CAAC,EAAE,QAAQ,MAAI,EAAE,MAAM,IAAG,OAAO,IAAI,EAAE;IAAG,IAAI,IAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAC,EAAE,EAAE,CAAC,GAAC,GAAE,IAAE,IAAG,CAAC,KAAG,KAAK,GAAG,CAAC,MAAI,IAAE,IAAE,CAAC,IAAE,EAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC,EAAC,CAAC,IAAE,CAAC,IAAE,EAAE,MAAM,GAAC,CAAC,IAAE,CAAC,KAAG,CAAC,KAAG,KAAG,KAAG,KAAG,CAAC,IAAE,MAAI,IAAI,GAAE,IAAE,EAAE,GAAE,IAAE,IAAG,IAAE,EAAE,CAAC,IAAE,CAAC,IAAE,KAAG,CAAC,IAAE,KAAG,CAAC,IAAE,IAAE,CAAC,IAAE,CAAC,CAAC,GAAE,KAAG,IAAE,IAAE,IAAE,OAAK,IAAE,CAAC,IAAE,EAAE,aAAa,IAAG,IAAE,EAAE,KAAK,CAAC,GAAE,EAAE,OAAO,CAAC,OAAK,KAAG,CAAC,GAAE,IAAE,IAAI,EAAE,IAAG,EAAE,CAAC,GAAC,EAAE,CAAC,IAAE,IAAE,IAAI,EAAE,EAAE,QAAQ,KAAI,IAAE,CAAC,IAAE,EAAE,SAAS,IAAE,IAAI,IAAG,IAAE,GAAE,IAAE,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC,IAAG,IAAE,EAAE,IAAI,CAAC,IAAG,IAAE,EAAE,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC,IAAG,EAAE,IAAI,CAAC,IAAG,IAAE,GAAE,IAAG,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,GAAE,OAAK,CAAC,IAAE,EAAE,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,GAAE,IAAG,IAAG,IAAE,EAAE,KAAK,CAAC,IAAE,GAAE,IAAE,IAAG,KAAG,UAAQ,CAAC,KAAG,KAAG,QAAO;QAAC,IAAG,CAAC,KAAG,CAAC,EAAE,GAAE,IAAE,GAAE,IAAG,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,GAAE;YAAC,IAAE;YAAE;QAAK;QAAC,KAAG,GAAE,IAAE;IAAC,OAAK;QAAC,CAAC,CAAC,CAAC,KAAG,CAAC,CAAC,EAAE,KAAK,CAAC,MAAI,EAAE,MAAM,CAAC,MAAI,GAAG,KAAG,CAAC,EAAE,GAAE,IAAE,GAAE,IAAG,IAAE,CAAC,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE;QAAE;IAAK;IAAC,OAAO,IAAE,CAAC,GAAE,EAAE,GAAE,GAAE,EAAE,QAAQ,EAAC;AAAE;AAAE,EAAE,aAAa,GAAC,EAAE,EAAE,GAAC;IAAW,IAAI,GAAE,IAAE,IAAI,CAAC,CAAC,EAAC,IAAE;IAAI,IAAG,GAAE;QAAC,IAAG,IAAE,EAAE,MAAM,GAAC,GAAE,IAAE,CAAC,IAAE,EAAE,IAAI,CAAC,CAAC,GAAC,EAAE,IAAE,GAAE,IAAE,CAAC,CAAC,EAAE,EAAC,GAAE,MAAK,IAAE,MAAI,GAAE,KAAG,GAAG;QAAI,IAAE,KAAG,CAAC,IAAE,CAAC;IAAC;IAAC,OAAO;AAAC;AAAE,EAAE,SAAS,GAAC,EAAE,GAAG,GAAC,SAAS,CAAC;IAAE,OAAO,EAAE,IAAI,EAAC,IAAI,IAAI,CAAC,WAAW,CAAC;AAAG;AAAE,EAAE,kBAAkB,GAAC,EAAE,QAAQ,GAAC,SAAS,CAAC;IAAE,IAAI,IAAE,IAAI,EAAC,IAAE,EAAE,WAAW;IAAC,OAAO,EAAE,EAAE,GAAE,IAAI,EAAE,IAAG,GAAE,GAAE,IAAG,EAAE,SAAS,EAAC,EAAE,QAAQ;AAAC;AAAE,EAAE,MAAM,GAAC,EAAE,EAAE,GAAC,SAAS,CAAC;IAAE,OAAO,IAAI,CAAC,GAAG,CAAC,OAAK;AAAC;AAAE,EAAE,KAAK,GAAC;IAAW,OAAO,EAAE,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,GAAE,IAAI,CAAC,CAAC,GAAC,GAAE;AAAE;AAAE,EAAE,WAAW,GAAC,EAAE,EAAE,GAAC,SAAS,CAAC;IAAE,OAAO,IAAI,CAAC,GAAG,CAAC,KAAG;AAAC;AAAE,EAAE,oBAAoB,GAAC,EAAE,GAAG,GAAC,SAAS,CAAC;IAAE,IAAI,IAAE,IAAI,CAAC,GAAG,CAAC;IAAG,OAAO,KAAG,KAAG,MAAI;AAAC;AAAE,EAAE,gBAAgB,GAAC,EAAE,IAAI,GAAC;IAAW,IAAI,GAAE,GAAE,GAAE,GAAE,GAAE,IAAE,IAAI,EAAC,IAAE,EAAE,WAAW,EAAC,IAAE,IAAI,EAAE;IAAG,IAAG,CAAC,EAAE,QAAQ,IAAG,OAAO,IAAI,EAAE,EAAE,CAAC,GAAC,IAAE,IAAE;IAAK,IAAG,EAAE,MAAM,IAAG,OAAO;IAAE,IAAE,EAAE,SAAS,EAAC,IAAE,EAAE,QAAQ,EAAC,EAAE,SAAS,GAAC,IAAE,KAAK,GAAG,CAAC,EAAE,CAAC,EAAC,EAAE,EAAE,MAAI,GAAE,EAAE,QAAQ,GAAC,GAAE,IAAE,EAAE,CAAC,CAAC,MAAM,EAAC,IAAE,KAAG,CAAC,IAAE,KAAK,IAAI,CAAC,IAAE,IAAG,IAAE,CAAC,IAAE,GAAG,GAAE,EAAE,EAAE,QAAQ,EAAE,IAAE,CAAC,IAAE,IAAG,IAAE,8BAA8B,GAAE,IAAE,EAAE,GAAE,GAAE,EAAE,KAAK,CAAC,IAAG,IAAI,EAAE,IAAG,CAAC;IAAG,IAAI,IAAI,GAAE,IAAE,GAAE,IAAE,IAAI,EAAE,IAAG,KAAK,IAAE,EAAE,KAAK,CAAC,IAAG,IAAE,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC;IAAM,OAAO,EAAE,GAAE,EAAE,SAAS,GAAC,GAAE,EAAE,QAAQ,GAAC,GAAE,CAAC;AAAE;AAAE,EAAE,cAAc,GAAC,EAAE,IAAI,GAAC;IAAW,IAAI,GAAE,GAAE,GAAE,GAAE,IAAE,IAAI,EAAC,IAAE,EAAE,WAAW;IAAC,IAAG,CAAC,EAAE,QAAQ,MAAI,EAAE,MAAM,IAAG,OAAO,IAAI,EAAE;IAAG,IAAG,IAAE,EAAE,SAAS,EAAC,IAAE,EAAE,QAAQ,EAAC,EAAE,SAAS,GAAC,IAAE,KAAK,GAAG,CAAC,EAAE,CAAC,EAAC,EAAE,EAAE,MAAI,GAAE,EAAE,QAAQ,GAAC,GAAE,IAAE,EAAE,CAAC,CAAC,MAAM,EAAC,IAAE,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,CAAC;SAAO;QAAC,IAAE,MAAI,KAAK,IAAI,CAAC,IAAG,IAAE,IAAE,KAAG,KAAG,IAAE,GAAE,IAAE,EAAE,KAAK,CAAC,IAAE,GAAG,GAAE,KAAI,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,CAAC;QAAG,IAAI,IAAI,GAAE,IAAE,IAAI,EAAE,IAAG,IAAE,IAAI,EAAE,KAAI,IAAE,IAAI,EAAE,KAAI,KAAK,IAAE,EAAE,KAAK,CAAC,IAAG,IAAE,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC;IAAK;IAAC,OAAO,EAAE,SAAS,GAAC,GAAE,EAAE,QAAQ,GAAC,GAAE,EAAE,GAAE,GAAE,GAAE,CAAC;AAAE;AAAE,EAAE,iBAAiB,GAAC,EAAE,IAAI,GAAC;IAAW,IAAI,GAAE,GAAE,IAAE,IAAI,EAAC,IAAE,EAAE,WAAW;IAAC,OAAO,EAAE,QAAQ,KAAG,EAAE,MAAM,KAAG,IAAI,EAAE,KAAG,CAAC,IAAE,EAAE,SAAS,EAAC,IAAE,EAAE,QAAQ,EAAC,EAAE,SAAS,GAAC,IAAE,GAAE,EAAE,QAAQ,GAAC,GAAE,EAAE,EAAE,IAAI,IAAG,EAAE,IAAI,IAAG,EAAE,SAAS,GAAC,GAAE,EAAE,QAAQ,GAAC,EAAE,IAAE,IAAI,EAAE,EAAE,CAAC;AAAC;AAAE,EAAE,aAAa,GAAC,EAAE,IAAI,GAAC;IAAW,IAAI,IAAE,IAAI,EAAC,IAAE,EAAE,WAAW,EAAC,IAAE,EAAE,GAAG,GAAG,GAAG,CAAC,IAAG,IAAE,EAAE,SAAS,EAAC,IAAE,EAAE,QAAQ;IAAC,OAAO,MAAI,CAAC,IAAE,MAAI,IAAE,EAAE,KAAK,KAAG,EAAE,GAAE,GAAE,KAAG,IAAI,EAAE,KAAG,IAAI,EAAE,OAAK,EAAE,MAAM,KAAG,EAAE,GAAE,IAAE,GAAE,GAAG,KAAK,CAAC,MAAI,CAAC,EAAE,SAAS,GAAC,IAAE,GAAE,EAAE,QAAQ,GAAC,GAAE,IAAE,IAAI,EAAE,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,IAAI,IAAI,GAAG,IAAI,IAAG,EAAE,SAAS,GAAC,GAAE,EAAE,QAAQ,GAAC,GAAE,EAAE,KAAK,CAAC,EAAE;AAAC;AAAE,EAAE,uBAAuB,GAAC,EAAE,KAAK,GAAC;IAAW,IAAI,GAAE,GAAE,IAAE,IAAI,EAAC,IAAE,EAAE,WAAW;IAAC,OAAO,EAAE,GAAG,CAAC,KAAG,IAAI,EAAE,EAAE,EAAE,CAAC,KAAG,IAAE,OAAK,EAAE,QAAQ,KAAG,CAAC,IAAE,EAAE,SAAS,EAAC,IAAE,EAAE,QAAQ,EAAC,EAAE,SAAS,GAAC,IAAE,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,GAAE,EAAE,EAAE,MAAI,GAAE,EAAE,QAAQ,GAAC,GAAE,IAAE,CAAC,GAAE,IAAE,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,IAAG,IAAE,CAAC,GAAE,EAAE,SAAS,GAAC,GAAE,EAAE,QAAQ,GAAC,GAAE,EAAE,EAAE,EAAE,IAAE,IAAI,EAAE;AAAE;AAAE,EAAE,qBAAqB,GAAC,EAAE,KAAK,GAAC;IAAW,IAAI,GAAE,GAAE,IAAE,IAAI,EAAC,IAAE,EAAE,WAAW;IAAC,OAAM,CAAC,EAAE,QAAQ,MAAI,EAAE,MAAM,KAAG,IAAI,EAAE,KAAG,CAAC,IAAE,EAAE,SAAS,EAAC,IAAE,EAAE,QAAQ,EAAC,EAAE,SAAS,GAAC,IAAE,IAAE,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,GAAE,EAAE,EAAE,MAAI,GAAE,EAAE,QAAQ,GAAC,GAAE,IAAE,CAAC,GAAE,IAAE,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,IAAG,IAAE,CAAC,GAAE,EAAE,SAAS,GAAC,GAAE,EAAE,QAAQ,GAAC,GAAE,EAAE,EAAE,EAAE;AAAC;AAAE,EAAE,wBAAwB,GAAC,EAAE,KAAK,GAAC;IAAW,IAAI,GAAE,GAAE,GAAE,GAAE,IAAE,IAAI,EAAC,IAAE,EAAE,WAAW;IAAC,OAAO,EAAE,QAAQ,KAAG,EAAE,CAAC,IAAE,IAAE,IAAI,EAAE,EAAE,GAAG,GAAG,EAAE,CAAC,KAAG,EAAE,CAAC,GAAC,IAAE,EAAE,MAAM,KAAG,IAAE,OAAK,CAAC,IAAE,EAAE,SAAS,EAAC,IAAE,EAAE,QAAQ,EAAC,IAAE,EAAE,EAAE,IAAG,KAAK,GAAG,CAAC,GAAE,KAAG,IAAE,CAAC,EAAE,CAAC,GAAC,IAAE,EAAE,IAAI,EAAE,IAAG,GAAE,GAAE,CAAC,KAAG,CAAC,EAAE,SAAS,GAAC,IAAE,IAAE,EAAE,CAAC,EAAC,IAAE,EAAE,EAAE,IAAI,CAAC,IAAG,IAAI,EAAE,GAAG,KAAK,CAAC,IAAG,IAAE,GAAE,IAAG,EAAE,SAAS,GAAC,IAAE,GAAE,EAAE,QAAQ,GAAC,GAAE,IAAE,EAAE,EAAE,IAAG,EAAE,SAAS,GAAC,GAAE,EAAE,QAAQ,GAAC,GAAE,EAAE,KAAK,CAAC,GAAG,CAAC,IAAE,IAAI,EAAE;AAAI;AAAE,EAAE,WAAW,GAAC,EAAE,IAAI,GAAC;IAAW,IAAI,GAAE,GAAE,GAAE,GAAE,IAAE,IAAI,EAAC,IAAE,EAAE,WAAW;IAAC,OAAO,EAAE,MAAM,KAAG,IAAI,EAAE,KAAG,CAAC,IAAE,EAAE,GAAG,GAAG,GAAG,CAAC,IAAG,IAAE,EAAE,SAAS,EAAC,IAAE,EAAE,QAAQ,EAAC,MAAI,CAAC,IAAE,MAAI,IAAE,CAAC,IAAE,EAAE,GAAE,IAAE,GAAE,GAAG,KAAK,CAAC,KAAI,EAAE,CAAC,GAAC,EAAE,CAAC,EAAC,CAAC,IAAE,IAAI,EAAE,OAAK,CAAC,EAAE,SAAS,GAAC,IAAE,GAAE,EAAE,QAAQ,GAAC,GAAE,IAAE,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,KAAK,CAAC,EAAE,KAAK,CAAC,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,IAAG,EAAE,SAAS,GAAC,GAAE,EAAE,QAAQ,GAAC,GAAE,EAAE,KAAK,CAAC,EAAE,CAAC;AAAC;AAAE,EAAE,cAAc,GAAC,EAAE,IAAI,GAAC;IAAW,IAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAE,IAAI,EAAC,IAAE,EAAE,WAAW,EAAC,IAAE,EAAE,SAAS,EAAC,IAAE,EAAE,QAAQ;IAAC,IAAG,EAAE,QAAQ,IAAG;QAAC,IAAG,EAAE,MAAM,IAAG,OAAO,IAAI,EAAE;QAAG,IAAG,EAAE,GAAG,GAAG,EAAE,CAAC,MAAI,IAAE,KAAG,IAAG,OAAO,IAAE,EAAE,GAAE,IAAE,GAAE,GAAG,KAAK,CAAC,MAAK,EAAE,CAAC,GAAC,EAAE,CAAC,EAAC;IAAC,OAAK;QAAC,IAAG,CAAC,EAAE,CAAC,EAAC,OAAO,IAAI,EAAE;QAAK,IAAG,IAAE,KAAG,IAAG,OAAO,IAAE,EAAE,GAAE,IAAE,GAAE,GAAG,KAAK,CAAC,KAAI,EAAE,CAAC,GAAC,EAAE,CAAC,EAAC;IAAC;IAAC,IAAI,EAAE,SAAS,GAAC,IAAE,IAAE,IAAG,EAAE,QAAQ,GAAC,GAAE,IAAE,KAAK,GAAG,CAAC,IAAG,IAAE,IAAE,IAAE,IAAG,IAAE,GAAE,GAAE,EAAE,EAAE,IAAE,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC;IAAI,IAAI,IAAE,CAAC,GAAE,IAAE,KAAK,IAAI,CAAC,IAAE,IAAG,IAAE,GAAE,IAAE,EAAE,KAAK,CAAC,IAAG,IAAE,IAAI,EAAE,IAAG,IAAE,GAAE,MAAI,CAAC,GAAG,IAAG,IAAE,EAAE,KAAK,CAAC,IAAG,IAAE,EAAE,KAAK,CAAC,EAAE,GAAG,CAAC,KAAG,KAAI,IAAE,EAAE,KAAK,CAAC,IAAG,IAAE,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,KAAG,KAAI,EAAE,CAAC,CAAC,EAAE,KAAG,KAAK,GAAE,IAAI,IAAE,GAAE,EAAE,CAAC,CAAC,EAAE,KAAG,EAAE,CAAC,CAAC,EAAE,IAAE;IAAM,OAAO,KAAG,CAAC,IAAE,EAAE,KAAK,CAAC,KAAG,IAAE,EAAE,GAAE,IAAE,CAAC,GAAE,EAAE,GAAE,EAAE,SAAS,GAAC,GAAE,EAAE,QAAQ,GAAC,GAAE,CAAC;AAAE;AAAE,EAAE,QAAQ,GAAC;IAAW,OAAM,CAAC,CAAC,IAAI,CAAC,CAAC;AAAA;AAAE,EAAE,SAAS,GAAC,EAAE,KAAK,GAAC;IAAW,OAAM,CAAC,CAAC,IAAI,CAAC,CAAC,IAAE,EAAE,IAAI,CAAC,CAAC,GAAC,KAAG,IAAI,CAAC,CAAC,CAAC,MAAM,GAAC;AAAC;AAAE,EAAE,KAAK,GAAC;IAAW,OAAM,CAAC,IAAI,CAAC,CAAC;AAAA;AAAE,EAAE,UAAU,GAAC,EAAE,KAAK,GAAC;IAAW,OAAO,IAAI,CAAC,CAAC,GAAC;AAAC;AAAE,EAAE,UAAU,GAAC,EAAE,KAAK,GAAC;IAAW,OAAO,IAAI,CAAC,CAAC,GAAC;AAAC;AAAE,EAAE,MAAM,GAAC;IAAW,OAAM,CAAC,CAAC,IAAI,CAAC,CAAC,IAAE,IAAI,CAAC,CAAC,CAAC,EAAE,KAAG;AAAC;AAAE,EAAE,QAAQ,GAAC,EAAE,EAAE,GAAC,SAAS,CAAC;IAAE,OAAO,IAAI,CAAC,GAAG,CAAC,KAAG;AAAC;AAAE,EAAE,iBAAiB,GAAC,EAAE,GAAG,GAAC,SAAS,CAAC;IAAE,OAAO,IAAI,CAAC,GAAG,CAAC,KAAG;AAAC;AAAE,EAAE,SAAS,GAAC,EAAE,GAAG,GAAC,SAAS,CAAC;IAAE,IAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAE,IAAI,EAAC,IAAE,EAAE,WAAW,EAAC,IAAE,EAAE,SAAS,EAAC,IAAE,EAAE,QAAQ,EAAC,IAAE;IAAE,IAAG,KAAG,MAAK,IAAE,IAAI,EAAE,KAAI,IAAE,CAAC;SAAM;QAAC,IAAG,IAAE,IAAI,EAAE,IAAG,IAAE,EAAE,CAAC,EAAC,EAAE,CAAC,GAAC,KAAG,CAAC,KAAG,CAAC,CAAC,CAAC,EAAE,IAAE,EAAE,EAAE,CAAC,IAAG,OAAO,IAAI,EAAE;QAAK,IAAE,EAAE,EAAE,CAAC;IAAG;IAAC,IAAG,IAAE,EAAE,CAAC,EAAC,EAAE,CAAC,GAAC,KAAG,CAAC,KAAG,CAAC,CAAC,CAAC,EAAE,IAAE,EAAE,EAAE,CAAC,IAAG,OAAO,IAAI,EAAE,KAAG,CAAC,CAAC,CAAC,EAAE,GAAC,CAAC,IAAE,IAAE,EAAE,CAAC,IAAE,IAAE,MAAI,IAAE,IAAE,IAAE;IAAG,IAAG,GAAE,IAAG,EAAE,MAAM,GAAC,GAAE,IAAE,CAAC;SAAM;QAAC,IAAI,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,OAAK,GAAG,KAAG;QAAG,IAAE,MAAI;IAAC;IAAC,IAAG,IAAE,CAAC,GAAE,IAAE,IAAE,GAAE,IAAE,EAAE,GAAE,IAAG,IAAE,IAAE,GAAG,GAAE,IAAE,MAAI,EAAE,GAAE,IAAG,IAAE,EAAE,GAAE,GAAE,GAAE,IAAG,EAAE,EAAE,CAAC,EAAC,IAAE,GAAE,IAAG,GAAG,IAAG,KAAG,IAAG,IAAE,EAAE,GAAE,IAAG,IAAE,IAAE,GAAG,GAAE,IAAE,MAAI,EAAE,GAAE,IAAG,IAAE,EAAE,GAAE,GAAE,GAAE,IAAG,CAAC,GAAE;QAAC,CAAC,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,IAAE,GAAE,IAAE,MAAI,KAAG,QAAM,CAAC,IAAE,EAAE,GAAE,IAAE,GAAE,EAAE;QAAE;IAAK;WAAO,EAAE,EAAE,CAAC,EAAC,KAAG,IAAG,GAAI;IAAA,OAAO,IAAE,CAAC,GAAE,EAAE,GAAE,GAAE;AAAE;AAAE,EAAE,KAAK,GAAC,EAAE,GAAG,GAAC,SAAS,CAAC;IAAE,IAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAE,IAAI,EAAC,IAAE,EAAE,WAAW;IAAC,IAAG,IAAE,IAAI,EAAE,IAAG,CAAC,EAAE,CAAC,IAAE,CAAC,EAAE,CAAC,EAAC,OAAM,CAAC,EAAE,CAAC,IAAE,CAAC,EAAE,CAAC,GAAC,IAAE,IAAI,EAAE,OAAK,EAAE,CAAC,GAAC,EAAE,CAAC,GAAC,CAAC,EAAE,CAAC,GAAC,IAAE,IAAI,EAAE,EAAE,CAAC,IAAE,EAAE,CAAC,KAAG,EAAE,CAAC,GAAC,IAAE,MAAK;IAAE,IAAG,EAAE,CAAC,IAAE,EAAE,CAAC,EAAC,OAAO,EAAE,CAAC,GAAC,CAAC,EAAE,CAAC,EAAC,EAAE,IAAI,CAAC;IAAG,IAAG,IAAE,EAAE,CAAC,EAAC,IAAE,EAAE,CAAC,EAAC,IAAE,EAAE,SAAS,EAAC,IAAE,EAAE,QAAQ,EAAC,CAAC,CAAC,CAAC,EAAE,IAAE,CAAC,CAAC,CAAC,EAAE,EAAC;QAAC,IAAG,CAAC,CAAC,EAAE,EAAC,EAAE,CAAC,GAAC,CAAC,EAAE,CAAC;aAAM,IAAG,CAAC,CAAC,EAAE,EAAC,IAAE,IAAI,EAAE;aAAQ,OAAO,IAAI,EAAE,MAAI,IAAE,CAAC,IAAE;QAAG,OAAO,IAAE,EAAE,GAAE,GAAE,KAAG;IAAC;IAAC,IAAG,IAAE,EAAE,EAAE,CAAC,GAAC,IAAG,IAAE,EAAE,EAAE,CAAC,GAAC,IAAG,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,GAAE,GAAE;QAAC,IAAI,IAAE,IAAE,GAAE,IAAE,CAAC,IAAE,GAAE,IAAE,CAAC,GAAE,IAAE,EAAE,MAAM,IAAE,CAAC,IAAE,GAAE,IAAE,GAAE,IAAE,EAAE,MAAM,GAAE,IAAE,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC,IAAE,IAAG,KAAG,GAAE,IAAE,KAAG,CAAC,IAAE,GAAE,EAAE,MAAM,GAAC,CAAC,GAAE,EAAE,OAAO,IAAG,IAAE,GAAE,KAAK,EAAE,IAAI,CAAC;QAAG,EAAE,OAAO;IAAE,OAAK;QAAC,IAAI,IAAE,EAAE,MAAM,EAAC,IAAE,EAAE,MAAM,EAAC,IAAE,IAAE,GAAE,KAAG,CAAC,IAAE,CAAC,GAAE,IAAE,GAAE,IAAE,GAAE,IAAI,IAAG,CAAC,CAAC,EAAE,IAAE,CAAC,CAAC,EAAE,EAAC;YAAC,IAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE;YAAC;QAAK;QAAC,IAAE;IAAC;IAAC,IAAI,KAAG,CAAC,IAAE,GAAE,IAAE,GAAE,IAAE,GAAE,EAAE,CAAC,GAAC,CAAC,EAAE,CAAC,GAAE,IAAE,EAAE,MAAM,EAAC,IAAE,EAAE,MAAM,GAAC,GAAE,IAAE,GAAE,EAAE,EAAE,CAAC,CAAC,IAAI,GAAC;IAAE,IAAI,IAAE,EAAE,MAAM,EAAC,IAAE,GAAG;QAAC,IAAG,CAAC,CAAC,EAAE,EAAE,GAAC,CAAC,CAAC,EAAE,EAAC;YAAC,IAAI,IAAE,GAAE,KAAG,CAAC,CAAC,EAAE,EAAE,KAAG,GAAG,CAAC,CAAC,EAAE,GAAC,IAAE;YAAE,EAAE,CAAC,CAAC,EAAE,EAAC,CAAC,CAAC,EAAE,IAAE;QAAC;QAAC,CAAC,CAAC,EAAE,IAAE,CAAC,CAAC,EAAE;IAAA;IAAC,MAAK,CAAC,CAAC,EAAE,EAAE,KAAG,GAAG,EAAE,GAAG;IAAG,MAAK,CAAC,CAAC,EAAE,KAAG,GAAE,EAAE,KAAK,GAAG,EAAE;IAAE,OAAO,CAAC,CAAC,EAAE,GAAC,CAAC,EAAE,CAAC,GAAC,GAAE,EAAE,CAAC,GAAC,GAAG,GAAE,IAAG,IAAE,EAAE,GAAE,GAAE,KAAG,CAAC,IAAE,IAAI,EAAE,MAAI,IAAE,CAAC,IAAE;AAAE;AAAE,EAAE,MAAM,GAAC,EAAE,GAAG,GAAC,SAAS,CAAC;IAAE,IAAI,GAAE,IAAE,IAAI,EAAC,IAAE,EAAE,WAAW;IAAC,OAAO,IAAE,IAAI,EAAE,IAAG,CAAC,EAAE,CAAC,IAAE,CAAC,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAC,IAAI,EAAE,OAAK,CAAC,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAC,EAAE,IAAI,EAAE,IAAG,EAAE,SAAS,EAAC,EAAE,QAAQ,IAAE,CAAC,IAAE,CAAC,GAAE,EAAE,MAAM,IAAE,IAAE,CAAC,IAAE,EAAE,GAAE,EAAE,GAAG,IAAG,GAAE,GAAE,IAAG,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,IAAE,EAAE,GAAE,GAAE,GAAE,EAAE,MAAM,EAAC,IAAG,IAAE,EAAE,KAAK,CAAC,IAAG,IAAE,CAAC,GAAE,EAAE,KAAK,CAAC,EAAE;AAAC;AAAE,EAAE,kBAAkB,GAAC,EAAE,GAAG,GAAC;IAAW,OAAO,GAAG,IAAI;AAAC;AAAE,EAAE,gBAAgB,GAAC,EAAE,EAAE,GAAC;IAAW,OAAO,EAAE,IAAI;AAAC;AAAE,EAAE,OAAO,GAAC,EAAE,GAAG,GAAC;IAAW,IAAI,IAAE,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI;IAAE,OAAO,EAAE,CAAC,GAAC,CAAC,EAAE,CAAC,EAAC,EAAE;AAAE;AAAE,EAAE,IAAI,GAAC,EAAE,GAAG,GAAC,SAAS,CAAC;IAAE,IAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAE,IAAI,EAAC,IAAE,EAAE,WAAW;IAAC,IAAG,IAAE,IAAI,EAAE,IAAG,CAAC,EAAE,CAAC,IAAE,CAAC,EAAE,CAAC,EAAC,OAAM,CAAC,EAAE,CAAC,IAAE,CAAC,EAAE,CAAC,GAAC,IAAE,IAAI,EAAE,OAAK,EAAE,CAAC,IAAE,CAAC,IAAE,IAAI,EAAE,EAAE,CAAC,IAAE,EAAE,CAAC,KAAG,EAAE,CAAC,GAAC,IAAE,IAAI,GAAE;IAAE,IAAG,EAAE,CAAC,IAAE,EAAE,CAAC,EAAC,OAAO,EAAE,CAAC,GAAC,CAAC,EAAE,CAAC,EAAC,EAAE,KAAK,CAAC;IAAG,IAAG,IAAE,EAAE,CAAC,EAAC,IAAE,EAAE,CAAC,EAAC,IAAE,EAAE,SAAS,EAAC,IAAE,EAAE,QAAQ,EAAC,CAAC,CAAC,CAAC,EAAE,IAAE,CAAC,CAAC,CAAC,EAAE,EAAC,OAAO,CAAC,CAAC,EAAE,IAAE,CAAC,IAAE,IAAI,EAAE,EAAE,GAAE,IAAE,EAAE,GAAE,GAAE,KAAG;IAAE,IAAG,IAAE,EAAE,EAAE,CAAC,GAAC,IAAG,IAAE,EAAE,EAAE,CAAC,GAAC,IAAG,IAAE,EAAE,KAAK,IAAG,IAAE,IAAE,GAAE,GAAE;QAAC,IAAI,IAAE,IAAE,CAAC,IAAE,GAAE,IAAE,CAAC,GAAE,IAAE,EAAE,MAAM,IAAE,CAAC,IAAE,GAAE,IAAE,GAAE,IAAE,EAAE,MAAM,GAAE,IAAE,KAAK,IAAI,CAAC,IAAE,IAAG,IAAE,IAAE,IAAE,IAAE,IAAE,IAAE,GAAE,IAAE,KAAG,CAAC,IAAE,GAAE,EAAE,MAAM,GAAC,CAAC,GAAE,EAAE,OAAO,IAAG,KAAK,EAAE,IAAI,CAAC;QAAG,EAAE,OAAO;IAAE;IAAC,IAAI,IAAE,EAAE,MAAM,EAAC,IAAE,EAAE,MAAM,EAAC,IAAE,IAAE,KAAG,CAAC,IAAE,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,CAAC,GAAE,IAAE,GAAE,GAAG,IAAE,CAAC,CAAC,CAAC,EAAE,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,CAAC,IAAE,IAAE,GAAE,CAAC,CAAC,EAAE,IAAE;IAAE,IAAI,KAAG,CAAC,EAAE,OAAO,CAAC,IAAG,EAAE,CAAC,GAAE,IAAE,EAAE,MAAM,EAAC,CAAC,CAAC,EAAE,EAAE,IAAE,GAAG,EAAE,GAAG;IAAG,OAAO,EAAE,CAAC,GAAC,GAAE,EAAE,CAAC,GAAC,GAAG,GAAE,IAAG,IAAE,EAAE,GAAE,GAAE,KAAG;AAAC;AAAE,EAAE,SAAS,GAAC,EAAE,EAAE,GAAC,SAAS,CAAC;IAAE,IAAI,GAAE,IAAE,IAAI;IAAC,IAAG,MAAI,KAAK,KAAG,MAAI,CAAC,CAAC,KAAG,MAAI,KAAG,MAAI,GAAE,MAAM,MAAM,IAAE;IAAG,OAAO,EAAE,CAAC,GAAC,CAAC,IAAE,GAAG,EAAE,CAAC,GAAE,KAAG,EAAE,CAAC,GAAC,IAAE,KAAG,CAAC,IAAE,EAAE,CAAC,GAAC,CAAC,CAAC,IAAE,IAAE,KAAI;AAAC;AAAE,EAAE,KAAK,GAAC;IAAW,IAAI,IAAE,IAAI,EAAC,IAAE,EAAE,WAAW;IAAC,OAAO,EAAE,IAAI,EAAE,IAAG,EAAE,CAAC,GAAC,GAAE,EAAE,QAAQ;AAAC;AAAE,EAAE,IAAI,GAAC,EAAE,GAAG,GAAC;IAAW,IAAI,GAAE,GAAE,IAAE,IAAI,EAAC,IAAE,EAAE,WAAW;IAAC,OAAO,EAAE,QAAQ,KAAG,EAAE,MAAM,KAAG,IAAI,EAAE,KAAG,CAAC,IAAE,EAAE,SAAS,EAAC,IAAE,EAAE,QAAQ,EAAC,EAAE,SAAS,GAAC,IAAE,KAAK,GAAG,CAAC,EAAE,CAAC,EAAC,EAAE,EAAE,MAAI,GAAE,EAAE,QAAQ,GAAC,GAAE,IAAE,GAAG,GAAE,GAAG,GAAE,KAAI,EAAE,SAAS,GAAC,GAAE,EAAE,QAAQ,GAAC,GAAE,EAAE,IAAE,IAAE,EAAE,GAAG,KAAG,GAAE,GAAE,GAAE,CAAC,EAAE,IAAE,IAAI,EAAE;AAAI;AAAE,EAAE,UAAU,GAAC,EAAE,IAAI,GAAC;IAAW,IAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAE,IAAI,EAAC,IAAE,EAAE,CAAC,EAAC,IAAE,EAAE,CAAC,EAAC,IAAE,EAAE,CAAC,EAAC,IAAE,EAAE,WAAW;IAAC,IAAG,MAAI,KAAG,CAAC,KAAG,CAAC,CAAC,CAAC,EAAE,EAAC,OAAO,IAAI,EAAE,CAAC,KAAG,IAAE,KAAG,CAAC,CAAC,KAAG,CAAC,CAAC,EAAE,IAAE,MAAI,IAAE,IAAE,IAAE;IAAG,IAAI,IAAE,CAAC,GAAE,IAAE,KAAK,IAAI,CAAC,CAAC,IAAG,KAAG,KAAG,KAAG,IAAE,IAAE,CAAC,IAAE,EAAE,IAAG,CAAC,EAAE,MAAM,GAAC,CAAC,IAAE,KAAG,KAAG,CAAC,KAAG,GAAG,GAAE,IAAE,KAAK,IAAI,CAAC,IAAG,IAAE,EAAE,CAAC,IAAE,CAAC,IAAE,KAAG,CAAC,IAAE,KAAG,IAAE,CAAC,GAAE,KAAG,IAAE,IAAE,IAAE,OAAK,IAAE,CAAC,IAAE,EAAE,aAAa,IAAG,IAAE,EAAE,KAAK,CAAC,GAAE,EAAE,OAAO,CAAC,OAAK,KAAG,CAAC,GAAE,IAAE,IAAI,EAAE,EAAE,IAAE,IAAE,IAAI,EAAE,EAAE,QAAQ,KAAI,IAAE,CAAC,IAAE,EAAE,SAAS,IAAE,IAAI,IAAG,IAAE,GAAE,IAAE,EAAE,IAAI,CAAC,EAAE,GAAE,GAAE,IAAE,GAAE,IAAI,KAAK,CAAC,KAAI,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,GAAE,OAAK,CAAC,IAAE,EAAE,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,GAAE,IAAG,IAAG,IAAE,EAAE,KAAK,CAAC,IAAE,GAAE,IAAE,IAAG,KAAG,UAAQ,CAAC,KAAG,KAAG,QAAO;QAAC,IAAG,CAAC,KAAG,CAAC,EAAE,GAAE,IAAE,GAAE,IAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,GAAE;YAAC,IAAE;YAAE;QAAK;QAAC,KAAG,GAAE,IAAE;IAAC,OAAK;QAAC,CAAC,CAAC,CAAC,KAAG,CAAC,CAAC,EAAE,KAAK,CAAC,MAAI,EAAE,MAAM,CAAC,MAAI,GAAG,KAAG,CAAC,EAAE,GAAE,IAAE,GAAE,IAAG,IAAE,CAAC,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE;QAAE;IAAK;IAAC,OAAO,IAAE,CAAC,GAAE,EAAE,GAAE,GAAE,EAAE,QAAQ,EAAC;AAAE;AAAE,EAAE,OAAO,GAAC,EAAE,GAAG,GAAC;IAAW,IAAI,GAAE,GAAE,IAAE,IAAI,EAAC,IAAE,EAAE,WAAW;IAAC,OAAO,EAAE,QAAQ,KAAG,EAAE,MAAM,KAAG,IAAI,EAAE,KAAG,CAAC,IAAE,EAAE,SAAS,EAAC,IAAE,EAAE,QAAQ,EAAC,EAAE,SAAS,GAAC,IAAE,IAAG,EAAE,QAAQ,GAAC,GAAE,IAAE,EAAE,GAAG,IAAG,EAAE,CAAC,GAAC,GAAE,IAAE,EAAE,GAAE,IAAI,EAAE,GAAG,KAAK,CAAC,EAAE,KAAK,CAAC,IAAI,IAAI,IAAG,IAAE,IAAG,IAAG,EAAE,SAAS,GAAC,GAAE,EAAE,QAAQ,GAAC,GAAE,EAAE,KAAG,KAAG,KAAG,IAAE,EAAE,GAAG,KAAG,GAAE,GAAE,GAAE,CAAC,EAAE,IAAE,IAAI,EAAE;AAAI;AAAE,EAAE,KAAK,GAAC,EAAE,GAAG,GAAC,SAAS,CAAC;IAAE,IAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAE,IAAI,EAAC,IAAE,EAAE,WAAW,EAAC,IAAE,EAAE,CAAC,EAAC,IAAE,CAAC,IAAE,IAAI,EAAE,EAAE,EAAE,CAAC;IAAC,IAAG,EAAE,CAAC,IAAE,EAAE,CAAC,EAAC,CAAC,KAAG,CAAC,CAAC,CAAC,EAAE,IAAE,CAAC,KAAG,CAAC,CAAC,CAAC,EAAE,EAAC,OAAO,IAAI,EAAE,CAAC,EAAE,CAAC,IAAE,KAAG,CAAC,CAAC,CAAC,EAAE,IAAE,CAAC,KAAG,KAAG,CAAC,CAAC,CAAC,EAAE,IAAE,CAAC,IAAE,MAAI,CAAC,KAAG,CAAC,IAAE,EAAE,CAAC,GAAC,IAAE,EAAE,CAAC,GAAC;IAAG,IAAI,IAAE,EAAE,EAAE,CAAC,GAAC,KAAG,EAAE,EAAE,CAAC,GAAC,IAAG,IAAE,EAAE,MAAM,EAAC,IAAE,EAAE,MAAM,EAAC,IAAE,KAAG,CAAC,IAAE,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,CAAC,GAAE,IAAE,EAAE,EAAC,IAAE,IAAE,GAAE,IAAE,GAAE,KAAK,EAAE,IAAI,CAAC;IAAG,IAAI,IAAE,GAAE,EAAE,KAAG,GAAG;QAAC,IAAI,IAAE,GAAE,IAAE,IAAE,GAAE,IAAE,GAAG,IAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,IAAE,IAAE,EAAE,GAAC,GAAE,CAAC,CAAC,IAAI,GAAC,IAAE,IAAE,GAAE,IAAE,IAAE,IAAE;QAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,CAAC,EAAE,GAAC,CAAC,IAAE,IAAE;IAAC;IAAC,MAAK,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG;IAAG,OAAO,IAAE,EAAE,IAAE,EAAE,KAAK,IAAG,EAAE,CAAC,GAAC,GAAE,EAAE,CAAC,GAAC,GAAG,GAAE,IAAG,IAAE,EAAE,GAAE,EAAE,SAAS,EAAC,EAAE,QAAQ,IAAE;AAAC;AAAE,EAAE,QAAQ,GAAC,SAAS,CAAC,EAAC,CAAC;IAAE,OAAO,GAAG,IAAI,EAAC,GAAE,GAAE;AAAE;AAAE,EAAE,eAAe,GAAC,EAAE,IAAI,GAAC,SAAS,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,IAAI,EAAC,IAAE,EAAE,WAAW;IAAC,OAAO,IAAE,IAAI,EAAE,IAAG,MAAI,KAAK,IAAE,IAAE,CAAC,EAAE,GAAE,GAAE,IAAG,MAAI,KAAK,IAAE,IAAE,EAAE,QAAQ,GAAC,EAAE,GAAE,GAAE,IAAG,EAAE,GAAE,IAAE,EAAE,CAAC,GAAC,GAAE,EAAE;AAAC;AAAE,EAAE,aAAa,GAAC,SAAS,CAAC,EAAC,CAAC;IAAE,IAAI,GAAE,IAAE,IAAI,EAAC,IAAE,EAAE,WAAW;IAAC,OAAO,MAAI,KAAK,IAAE,IAAE,EAAE,GAAE,CAAC,KAAG,CAAC,EAAE,GAAE,GAAE,IAAG,MAAI,KAAK,IAAE,IAAE,EAAE,QAAQ,GAAC,EAAE,GAAE,GAAE,IAAG,IAAE,EAAE,IAAI,EAAE,IAAG,IAAE,GAAE,IAAG,IAAE,EAAE,GAAE,CAAC,GAAE,IAAE,EAAE,GAAE,EAAE,KAAK,MAAI,CAAC,EAAE,MAAM,KAAG,MAAI,IAAE;AAAC;AAAE,EAAE,OAAO,GAAC,SAAS,CAAC,EAAC,CAAC;IAAE,IAAI,GAAE,GAAE,IAAE,IAAI,EAAC,IAAE,EAAE,WAAW;IAAC,OAAO,MAAI,KAAK,IAAE,IAAE,EAAE,KAAG,CAAC,EAAE,GAAE,GAAE,IAAG,MAAI,KAAK,IAAE,IAAE,EAAE,QAAQ,GAAC,EAAE,GAAE,GAAE,IAAG,IAAE,EAAE,IAAI,EAAE,IAAG,IAAE,EAAE,CAAC,GAAC,GAAE,IAAG,IAAE,EAAE,GAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAC,EAAE,GAAE,EAAE,KAAK,MAAI,CAAC,EAAE,MAAM,KAAG,MAAI,IAAE;AAAC;AAAE,EAAE,UAAU,GAAC,SAAS,CAAC;IAAE,IAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAE,IAAI,EAAC,IAAE,EAAE,CAAC,EAAC,IAAE,EAAE,WAAW;IAAC,IAAG,CAAC,GAAE,OAAO,IAAI,EAAE;IAAG,IAAG,IAAE,IAAE,IAAI,EAAE,IAAG,IAAE,IAAE,IAAI,EAAE,IAAG,IAAE,IAAI,EAAE,IAAG,IAAE,EAAE,CAAC,GAAC,GAAG,KAAG,EAAE,CAAC,GAAC,GAAE,IAAE,IAAE,GAAE,EAAE,CAAC,CAAC,EAAE,GAAC,EAAE,IAAG,IAAE,IAAE,IAAE,IAAE,IAAG,KAAG,MAAK,IAAE,IAAE,IAAE,IAAE;SAAM;QAAC,IAAG,IAAE,IAAI,EAAE,IAAG,CAAC,EAAE,KAAK,MAAI,EAAE,EAAE,CAAC,IAAG,MAAM,MAAM,IAAE;QAAG,IAAE,EAAE,EAAE,CAAC,KAAG,IAAE,IAAE,IAAE,IAAE;IAAC;IAAC,IAAI,IAAE,CAAC,GAAE,IAAE,IAAI,EAAE,EAAE,KAAI,IAAE,EAAE,SAAS,EAAC,EAAE,SAAS,GAAC,IAAE,EAAE,MAAM,GAAC,IAAE,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,IAAG,IAAE,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC,KAAI,EAAE,GAAG,CAAC,MAAI,GAAG,IAAE,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC,KAAI,IAAE,GAAE,IAAE,GAAE,IAAE,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,KAAI,IAAE;IAAE,OAAO,IAAE,EAAE,EAAE,KAAK,CAAC,IAAG,GAAE,GAAE,GAAE,IAAG,IAAE,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC,KAAI,IAAE,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC,KAAI,EAAE,CAAC,GAAC,EAAE,CAAC,GAAC,EAAE,CAAC,EAAC,IAAE,EAAE,GAAE,GAAE,GAAE,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,GAAE,GAAE,GAAE,GAAG,KAAK,CAAC,GAAG,GAAG,MAAI,IAAE;QAAC;QAAE;KAAE,GAAC;QAAC;QAAE;KAAE,EAAC,EAAE,SAAS,GAAC,GAAE,IAAE,CAAC,GAAE;AAAC;AAAE,EAAE,aAAa,GAAC,EAAE,KAAK,GAAC,SAAS,CAAC,EAAC,CAAC;IAAE,OAAO,GAAG,IAAI,EAAC,IAAG,GAAE;AAAE;AAAE,EAAE,SAAS,GAAC,SAAS,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,IAAI,EAAC,IAAE,EAAE,WAAW;IAAC,IAAG,IAAE,IAAI,EAAE,IAAG,KAAG,MAAK;QAAC,IAAG,CAAC,EAAE,CAAC,EAAC,OAAO;QAAE,IAAE,IAAI,EAAE,IAAG,IAAE,EAAE,QAAQ;IAAA,OAAK;QAAC,IAAG,IAAE,IAAI,EAAE,IAAG,MAAI,KAAK,IAAE,IAAE,EAAE,QAAQ,GAAC,EAAE,GAAE,GAAE,IAAG,CAAC,EAAE,CAAC,EAAC,OAAO,EAAE,CAAC,GAAC,IAAE;QAAE,IAAG,CAAC,EAAE,CAAC,EAAC,OAAO,EAAE,CAAC,IAAE,CAAC,EAAE,CAAC,GAAC,EAAE,CAAC,GAAE;IAAC;IAAC,OAAO,EAAE,CAAC,CAAC,EAAE,GAAC,CAAC,IAAE,CAAC,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAG,KAAK,CAAC,IAAG,IAAE,CAAC,GAAE,EAAE,EAAE,IAAE,CAAC,EAAE,CAAC,GAAC,EAAE,CAAC,EAAC,IAAE,CAAC,GAAE;AAAC;AAAE,EAAE,QAAQ,GAAC;IAAW,OAAM,CAAC,IAAI;AAAA;AAAE,EAAE,OAAO,GAAC,SAAS,CAAC,EAAC,CAAC;IAAE,OAAO,GAAG,IAAI,EAAC,GAAE,GAAE;AAAE;AAAE,EAAE,OAAO,GAAC,EAAE,GAAG,GAAC,SAAS,CAAC;IAAE,IAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAE,IAAI,EAAC,IAAE,EAAE,WAAW,EAAC,IAAE,CAAC,CAAC,IAAE,IAAI,EAAE,EAAE;IAAE,IAAG,CAAC,EAAE,CAAC,IAAE,CAAC,EAAE,CAAC,IAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAC,OAAO,IAAI,EAAE,EAAE,CAAC,GAAE;IAAI,IAAG,IAAE,IAAI,EAAE,IAAG,EAAE,EAAE,CAAC,IAAG,OAAO;IAAE,IAAG,IAAE,EAAE,SAAS,EAAC,IAAE,EAAE,QAAQ,EAAC,EAAE,EAAE,CAAC,IAAG,OAAO,EAAE,GAAE,GAAE;IAAG,IAAG,IAAE,EAAE,EAAE,CAAC,GAAC,IAAG,KAAG,EAAE,CAAC,CAAC,MAAM,GAAC,KAAG,CAAC,IAAE,IAAE,IAAE,CAAC,IAAE,CAAC,KAAG,IAAG,OAAO,IAAE,GAAG,GAAE,GAAE,GAAE,IAAG,EAAE,CAAC,GAAC,IAAE,IAAI,EAAE,GAAG,GAAG,CAAC,KAAG,EAAE,GAAE,GAAE;IAAG,IAAG,IAAE,EAAE,CAAC,EAAC,IAAE,GAAE;QAAC,IAAG,IAAE,EAAE,CAAC,CAAC,MAAM,GAAC,GAAE,OAAO,IAAI,EAAE;QAAK,IAAG,CAAC,EAAE,CAAC,CAAC,EAAE,GAAC,CAAC,KAAG,KAAG,CAAC,IAAE,CAAC,GAAE,EAAE,CAAC,IAAE,KAAG,EAAE,CAAC,CAAC,EAAE,IAAE,KAAG,EAAE,CAAC,CAAC,MAAM,IAAE,GAAE,OAAO,EAAE,CAAC,GAAC,GAAE;IAAC;IAAC,OAAO,IAAE,EAAE,CAAC,GAAE,IAAG,IAAE,KAAG,KAAG,CAAC,SAAS,KAAG,EAAE,IAAE,CAAC,KAAK,GAAG,CAAC,OAAK,EAAE,EAAE,CAAC,KAAG,KAAK,IAAI,GAAC,EAAE,CAAC,GAAC,CAAC,KAAG,IAAI,EAAE,IAAE,IAAI,CAAC,EAAC,IAAE,EAAE,IAAI,GAAC,KAAG,IAAE,EAAE,IAAI,GAAC,IAAE,IAAI,EAAE,IAAE,IAAE,IAAE,IAAE,KAAG,CAAC,IAAE,CAAC,GAAE,EAAE,QAAQ,GAAC,EAAE,CAAC,GAAC,GAAE,IAAE,KAAK,GAAG,CAAC,IAAG,CAAC,IAAE,EAAE,EAAE,MAAM,GAAE,IAAE,GAAG,EAAE,KAAK,CAAC,EAAE,GAAE,IAAE,KAAI,IAAG,EAAE,CAAC,IAAE,CAAC,IAAE,EAAE,GAAE,IAAE,GAAE,IAAG,EAAE,EAAE,CAAC,EAAC,GAAE,MAAI,CAAC,IAAE,IAAE,IAAG,IAAE,EAAE,GAAG,EAAE,KAAK,CAAC,EAAE,GAAE,IAAE,KAAI,IAAG,IAAE,GAAE,IAAG,CAAC,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,IAAE,GAAE,IAAE,MAAI,KAAG,QAAM,CAAC,IAAE,EAAE,GAAE,IAAE,GAAE,EAAE,CAAC,CAAC,GAAE,EAAE,CAAC,GAAC,GAAE,IAAE,CAAC,GAAE,EAAE,QAAQ,GAAC,GAAE,EAAE,GAAE,GAAE,EAAE;AAAC;AAAE,EAAE,WAAW,GAAC,SAAS,CAAC,EAAC,CAAC;IAAE,IAAI,GAAE,IAAE,IAAI,EAAC,IAAE,EAAE,WAAW;IAAC,OAAO,MAAI,KAAK,IAAE,IAAE,EAAE,GAAE,EAAE,CAAC,IAAE,EAAE,QAAQ,IAAE,EAAE,CAAC,IAAE,EAAE,QAAQ,IAAE,CAAC,EAAE,GAAE,GAAE,IAAG,MAAI,KAAK,IAAE,IAAE,EAAE,QAAQ,GAAC,EAAE,GAAE,GAAE,IAAG,IAAE,EAAE,IAAI,EAAE,IAAG,GAAE,IAAG,IAAE,EAAE,GAAE,KAAG,EAAE,CAAC,IAAE,EAAE,CAAC,IAAE,EAAE,QAAQ,EAAC,EAAE,GAAE,EAAE,KAAK,MAAI,CAAC,EAAE,MAAM,KAAG,MAAI,IAAE;AAAC;AAAE,EAAE,mBAAmB,GAAC,EAAE,IAAI,GAAC,SAAS,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,IAAI,EAAC,IAAE,EAAE,WAAW;IAAC,OAAO,MAAI,KAAK,IAAE,CAAC,IAAE,EAAE,SAAS,EAAC,IAAE,EAAE,QAAQ,IAAE,CAAC,EAAE,GAAE,GAAE,IAAG,MAAI,KAAK,IAAE,IAAE,EAAE,QAAQ,GAAC,EAAE,GAAE,GAAE,EAAE,GAAE,EAAE,IAAI,EAAE,IAAG,GAAE;AAAE;AAAE,EAAE,QAAQ,GAAC;IAAW,IAAI,IAAE,IAAI,EAAC,IAAE,EAAE,WAAW,EAAC,IAAE,EAAE,GAAE,EAAE,CAAC,IAAE,EAAE,QAAQ,IAAE,EAAE,CAAC,IAAE,EAAE,QAAQ;IAAE,OAAO,EAAE,KAAK,MAAI,CAAC,EAAE,MAAM,KAAG,MAAI,IAAE;AAAC;AAAE,EAAE,SAAS,GAAC,EAAE,KAAK,GAAC;IAAW,OAAO,EAAE,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,GAAE,IAAI,CAAC,CAAC,GAAC,GAAE;AAAE;AAAE,EAAE,OAAO,GAAC,EAAE,MAAM,GAAC;IAAW,IAAI,IAAE,IAAI,EAAC,IAAE,EAAE,WAAW,EAAC,IAAE,EAAE,GAAE,EAAE,CAAC,IAAE,EAAE,QAAQ,IAAE,EAAE,CAAC,IAAE,EAAE,QAAQ;IAAE,OAAO,EAAE,KAAK,KAAG,MAAI,IAAE;AAAC;AAAE,SAAS,EAAE,CAAC;IAAE,IAAI,GAAE,GAAE,GAAE,IAAE,EAAE,MAAM,GAAC,GAAE,IAAE,IAAG,IAAE,CAAC,CAAC,EAAE;IAAC,IAAG,IAAE,GAAE;QAAC,IAAI,KAAG,GAAE,IAAE,GAAE,IAAE,GAAE,IAAI,IAAE,CAAC,CAAC,EAAE,GAAC,IAAG,IAAE,IAAE,EAAE,MAAM,EAAC,KAAG,CAAC,KAAG,EAAE,EAAE,GAAE,KAAG;QAAE,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,IAAE,IAAG,IAAE,IAAE,EAAE,MAAM,EAAC,KAAG,CAAC,KAAG,EAAE,EAAE;IAAC,OAAM,IAAG,MAAI,GAAE,OAAM;IAAI,MAAK,IAAE,OAAK,GAAG,KAAG;IAAG,OAAO,IAAE;AAAC;AAAC,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,CAAC;IAAE,IAAG,MAAI,CAAC,CAAC,KAAG,IAAE,KAAG,IAAE,GAAE,MAAM,MAAM,IAAE;AAAE;AAAC,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;IAAE,IAAI,GAAE,GAAE,GAAE;IAAE,IAAI,IAAE,CAAC,CAAC,EAAE,EAAC,KAAG,IAAG,KAAG,GAAG,EAAE;IAAE,OAAM,EAAE,IAAE,IAAE,CAAC,KAAG,GAAE,IAAE,CAAC,IAAE,CAAC,IAAE,KAAK,IAAI,CAAC,CAAC,IAAE,CAAC,IAAE,IAAG,KAAG,CAAC,GAAE,IAAE,EAAE,IAAG,IAAE,IAAG,IAAE,CAAC,CAAC,EAAE,GAAC,IAAE,GAAE,KAAG,OAAK,IAAE,IAAE,CAAC,KAAG,IAAE,IAAE,IAAE,MAAI,IAAE,KAAG,KAAG,CAAC,IAAE,IAAE,KAAG,CAAC,GAAE,IAAE,IAAE,KAAG,KAAG,SAAO,IAAE,KAAG,KAAG,SAAO,KAAG,OAAK,KAAG,CAAC,IAAE,IAAE,CAAC,IAAE,KAAG,IAAE,KAAG,KAAG,IAAE,KAAG,IAAE,KAAG,IAAE,CAAC,KAAG,CAAC,CAAC,CAAC,IAAE,EAAE,GAAC,IAAE,MAAI,CAAC,KAAG,EAAE,IAAG,IAAE,KAAG,KAAG,CAAC,KAAG,IAAE,KAAG,KAAG,CAAC,KAAG,CAAC,CAAC,CAAC,IAAE,EAAE,GAAC,IAAE,MAAI,CAAC,KAAG,IAAE,IAAE,IAAE,CAAC,KAAG,IAAE,IAAE,IAAE,MAAI,IAAE,KAAG,IAAE,IAAE,IAAE,MAAI,IAAE,KAAG,KAAG,CAAC,IAAE,IAAE,KAAG,CAAC,GAAE,IAAE,CAAC,KAAG,IAAE,CAAC,KAAG,KAAG,QAAM,CAAC,KAAG,IAAE,KAAG,KAAG,IAAI,IAAE,IAAE,CAAC,CAAC,KAAG,IAAE,CAAC,KAAG,IAAE,KAAG,KAAG,CAAC,KAAG,IAAE,KAAG,IAAE,KAAG,IAAE,CAAC,KAAG,CAAC,CAAC,CAAC,IAAE,EAAE,GAAC,IAAE,MAAI,CAAC,KAAG,EAAE,IAAG,IAAE,KAAG,GAAE;AAAC;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC,EAAC,CAAC;IAAE,IAAI,IAAI,GAAE,IAAE;QAAC;KAAE,EAAC,GAAE,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAE,GAAG;QAAC,IAAI,IAAE,EAAE,MAAM,EAAC,KAAK,CAAC,CAAC,EAAE,IAAE;QAAE,IAAI,CAAC,CAAC,EAAE,IAAE,GAAG,OAAO,CAAC,EAAE,MAAM,CAAC,OAAM,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI,CAAC,CAAC,EAAE,GAAC,IAAE,KAAG,CAAC,CAAC,CAAC,IAAE,EAAE,KAAG,KAAK,KAAG,CAAC,CAAC,CAAC,IAAE,EAAE,GAAC,CAAC,GAAE,CAAC,CAAC,IAAE,EAAE,IAAE,CAAC,CAAC,EAAE,GAAC,IAAE,GAAE,CAAC,CAAC,EAAE,IAAE,CAAC;IAAC;IAAC,OAAO,EAAE,OAAO;AAAE;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAI,GAAE,GAAE;IAAE,IAAG,EAAE,MAAM,IAAG,OAAO;IAAE,IAAE,EAAE,CAAC,CAAC,MAAM,EAAC,IAAE,KAAG,CAAC,IAAE,KAAK,IAAI,CAAC,IAAE,IAAG,IAAE,CAAC,IAAE,GAAG,GAAE,EAAE,EAAE,QAAQ,EAAE,IAAE,CAAC,IAAE,IAAG,IAAE,8BAA8B,GAAE,EAAE,SAAS,IAAE,GAAE,IAAE,EAAE,GAAE,GAAE,EAAE,KAAK,CAAC,IAAG,IAAI,EAAE;IAAI,IAAI,IAAI,IAAE,GAAE,KAAK;QAAC,IAAI,IAAE,EAAE,KAAK,CAAC;QAAG,IAAE,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC;IAAE;IAAC,OAAO,EAAE,SAAS,IAAE,GAAE;AAAC;AAAC,IAAI,IAAE;IAAW,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,CAAC;QAAE,IAAI,GAAE,IAAE,GAAE,IAAE,EAAE,MAAM;QAAC,IAAI,IAAE,EAAE,KAAK,IAAG,KAAK,IAAE,CAAC,CAAC,EAAE,GAAC,IAAE,GAAE,CAAC,CAAC,EAAE,GAAC,IAAE,IAAE,GAAE,IAAE,IAAE,IAAE;QAAE,OAAO,KAAG,EAAE,OAAO,CAAC,IAAG;IAAC;IAAC,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;QAAE,IAAI,GAAE;QAAE,IAAG,KAAG,GAAE,IAAE,IAAE,IAAE,IAAE,CAAC;aAAO,IAAI,IAAE,IAAE,GAAE,IAAE,GAAE,IAAI,IAAG,CAAC,CAAC,EAAE,IAAE,CAAC,CAAC,EAAE,EAAC;YAAC,IAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,IAAE,CAAC;YAAE;QAAK;QAAC,OAAO;IAAC;IAAC,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;QAAE,IAAI,IAAI,IAAE,GAAE,KAAK,CAAC,CAAC,EAAE,IAAE,GAAE,IAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,IAAE,GAAE,CAAC,CAAC,EAAE,GAAC,IAAE,IAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE;QAAC,MAAK,CAAC,CAAC,CAAC,EAAE,IAAE,EAAE,MAAM,GAAC,GAAG,EAAE,KAAK;IAAE;IAAC,OAAO,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;QAAE,IAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,GAAE,GAAE,IAAG,GAAE,GAAE,KAAG,EAAE,WAAW,EAAC,KAAG,EAAE,CAAC,IAAE,EAAE,CAAC,GAAC,IAAE,CAAC,GAAE,IAAE,EAAE,CAAC,EAAC,IAAE,EAAE,CAAC;QAAC,IAAG,CAAC,KAAG,CAAC,CAAC,CAAC,EAAE,IAAE,CAAC,KAAG,CAAC,CAAC,CAAC,EAAE,EAAC,OAAO,IAAI,GAAG,CAAC,EAAE,CAAC,IAAE,CAAC,EAAE,CAAC,IAAE,CAAC,IAAE,KAAG,CAAC,CAAC,EAAE,IAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,IAAE,MAAI,KAAG,CAAC,CAAC,EAAE,IAAE,KAAG,CAAC,IAAE,KAAG,IAAE,KAAG;QAAG,IAAI,IAAE,CAAC,IAAE,GAAE,IAAE,EAAE,CAAC,GAAC,EAAE,CAAC,IAAE,CAAC,IAAE,GAAE,IAAE,GAAE,IAAE,EAAE,EAAE,CAAC,GAAC,KAAG,EAAE,EAAE,CAAC,GAAC,EAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAE,EAAE,MAAM,EAAC,IAAE,IAAI,GAAG,KAAI,IAAE,EAAE,CAAC,GAAC,EAAE,EAAC,IAAE,GAAE,CAAC,CAAC,EAAE,IAAE,CAAC,CAAC,CAAC,EAAE,IAAE,CAAC,GAAE;QAAK,IAAG,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,CAAC,EAAE,IAAE,CAAC,KAAG,KAAI,KAAG,OAAK,CAAC,IAAE,IAAE,GAAG,SAAS,EAAC,IAAE,GAAG,QAAQ,IAAE,IAAE,IAAE,IAAE,CAAC,EAAE,CAAC,GAAC,EAAE,CAAC,IAAE,IAAE,IAAE,GAAE,IAAE,GAAE,EAAE,IAAI,CAAC,IAAG,IAAE,CAAC;aAAM;YAAC,IAAG,IAAE,IAAE,IAAE,IAAE,GAAE,IAAE,GAAE,KAAG,GAAE;gBAAC,IAAI,IAAE,GAAE,IAAE,CAAC,CAAC,EAAE,EAAC,KAAI,CAAC,IAAE,KAAG,CAAC,KAAG,KAAI,IAAI,KAAG,IAAE,IAAE,CAAC,CAAC,CAAC,EAAE,IAAE,CAAC,GAAE,CAAC,CAAC,EAAE,GAAC,KAAG,IAAE,GAAE,IAAE,KAAG,IAAE;gBAAE,IAAE,KAAG,IAAE;YAAC,OAAK;gBAAC,IAAI,IAAE,IAAE,CAAC,CAAC,CAAC,EAAE,GAAC,CAAC,IAAE,GAAE,IAAE,KAAG,CAAC,IAAE,EAAE,GAAE,GAAE,IAAG,IAAE,EAAE,GAAE,GAAE,IAAG,IAAE,EAAE,MAAM,EAAC,IAAE,EAAE,MAAM,GAAE,IAAE,GAAE,IAAE,EAAE,KAAK,CAAC,GAAE,IAAG,IAAE,EAAE,MAAM,EAAC,IAAE,GAAG,CAAC,CAAC,IAAI,GAAC;gBAAE,IAAE,EAAE,KAAK,IAAG,EAAE,OAAO,CAAC,IAAG,KAAG,CAAC,CAAC,EAAE,EAAC,CAAC,CAAC,EAAE,IAAE,IAAE,KAAG,EAAE;gBAAG,GAAG,IAAE,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,IAAG,IAAE,IAAE,CAAC,IAAE,CAAC,CAAC,EAAE,EAAC,KAAG,KAAG,CAAC,IAAE,IAAE,IAAE,CAAC,CAAC,CAAC,EAAE,IAAE,CAAC,CAAC,GAAE,IAAE,IAAE,KAAG,GAAE,IAAE,IAAE,CAAC,KAAG,KAAG,CAAC,IAAE,IAAE,CAAC,GAAE,IAAE,EAAE,GAAE,GAAE,IAAG,IAAE,EAAE,MAAM,EAAC,IAAE,EAAE,MAAM,EAAC,IAAE,EAAE,GAAE,GAAE,GAAE,IAAG,KAAG,KAAG,CAAC,KAAI,EAAE,GAAE,IAAE,IAAE,IAAE,GAAE,GAAE,EAAE,CAAC,IAAE,CAAC,KAAG,KAAG,CAAC,IAAE,IAAE,CAAC,GAAE,IAAE,EAAE,KAAK,EAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAE,KAAG,EAAE,OAAO,CAAC,IAAG,EAAE,GAAE,GAAE,GAAE,IAAG,KAAG,CAAC,KAAG,CAAC,IAAE,EAAE,MAAM,EAAC,IAAE,EAAE,GAAE,GAAE,GAAE,IAAG,IAAE,KAAG,CAAC,KAAI,EAAE,GAAE,IAAE,IAAE,IAAE,GAAE,GAAE,EAAE,CAAC,GAAE,IAAE,EAAE,MAAM,IAAE,MAAI,KAAG,CAAC,KAAI,IAAE;oBAAC;iBAAE,GAAE,CAAC,CAAC,IAAI,GAAC,GAAE,KAAG,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,IAAI,GAAC,CAAC,CAAC,EAAE,IAAE,IAAE,CAAC,IAAE;oBAAC,CAAC,CAAC,EAAE;iBAAC,EAAC,IAAE,CAAC;uBAAQ,CAAC,MAAI,KAAG,CAAC,CAAC,EAAE,KAAG,KAAK,CAAC,KAAG,IAAK;gBAAA,IAAE,CAAC,CAAC,EAAE,KAAG,KAAK;YAAC;YAAC,CAAC,CAAC,EAAE,IAAE,EAAE,KAAK;QAAE;QAAC,IAAG,KAAG,GAAE,EAAE,CAAC,GAAC,GAAE,KAAG;aAAM;YAAC,IAAI,IAAE,GAAE,IAAE,CAAC,CAAC,EAAE,EAAC,KAAG,IAAG,KAAG,GAAG;YAAI,EAAE,CAAC,GAAC,IAAE,IAAE,IAAE,GAAE,EAAE,GAAE,IAAE,IAAE,EAAE,CAAC,GAAC,IAAE,GAAE,GAAE;QAAE;QAAC,OAAO;IAAC;AAAC;AAAI,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;IAAE,IAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAE,EAAE,WAAW;IAAC,GAAE,IAAG,KAAG,MAAK;QAAC,IAAG,IAAE,EAAE,CAAC,EAAC,CAAC,GAAE,OAAO;QAAE,IAAI,IAAE,GAAE,IAAE,CAAC,CAAC,EAAE,EAAC,KAAG,IAAG,KAAG,GAAG;QAAI,IAAG,IAAE,IAAE,GAAE,IAAE,GAAE,KAAG,GAAE,IAAE,GAAE,IAAE,CAAC,CAAC,IAAE,EAAE,EAAC,IAAE,IAAE,EAAE,IAAG,IAAE,IAAE,KAAG,KAAG;aAAO,IAAG,IAAE,KAAK,IAAI,CAAC,CAAC,IAAE,CAAC,IAAE,IAAG,IAAE,EAAE,MAAM,EAAC,KAAG,GAAE,IAAG,GAAE;YAAC,MAAK,OAAK,GAAG,EAAE,IAAI,CAAC;YAAG,IAAE,IAAE,GAAE,IAAE,GAAE,KAAG,GAAE,IAAE,IAAE,IAAE;QAAC,OAAM,MAAM;aAAM;YAAC,IAAI,IAAE,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,GAAE,KAAG,IAAG,KAAG,GAAG;YAAI,KAAG,GAAE,IAAE,IAAE,IAAE,GAAE,IAAE,IAAE,IAAE,IAAE,IAAE,EAAE,IAAG,IAAE,IAAE,KAAG,KAAG;QAAC;QAAC,IAAG,IAAE,KAAG,IAAE,KAAG,CAAC,CAAC,IAAE,EAAE,KAAG,KAAK,KAAG,CAAC,IAAE,IAAE,IAAE,IAAE,EAAE,IAAG,IAAE,IAAE,EAAE,GAAE,IAAE,IAAE,IAAE,CAAC,KAAG,CAAC,KAAG,CAAC,KAAG,KAAG,KAAG,CAAC,EAAE,CAAC,GAAC,IAAE,IAAE,CAAC,CAAC,IAAE,IAAE,KAAG,KAAG,KAAG,CAAC,KAAG,KAAG,KAAG,KAAG,KAAG,CAAC,IAAE,IAAE,IAAE,IAAE,IAAE,EAAE,IAAG,IAAE,KAAG,IAAE,CAAC,CAAC,IAAE,EAAE,IAAE,KAAG,KAAG,KAAG,CAAC,EAAE,CAAC,GAAC,IAAE,IAAE,CAAC,CAAC,GAAE,IAAE,KAAG,CAAC,CAAC,CAAC,EAAE,EAAC,OAAO,EAAE,MAAM,GAAC,GAAE,IAAE,CAAC,KAAG,EAAE,CAAC,GAAC,GAAE,CAAC,CAAC,EAAE,GAAC,EAAE,IAAG,CAAC,IAAE,IAAE,CAAC,IAAE,IAAG,EAAE,CAAC,GAAC,CAAC,KAAG,CAAC,IAAE,CAAC,CAAC,EAAE,GAAC,EAAE,CAAC,GAAC,GAAE;QAAE,IAAG,KAAG,IAAE,CAAC,EAAE,MAAM,GAAC,GAAE,IAAE,GAAE,GAAG,IAAE,CAAC,EAAE,MAAM,GAAC,IAAE,GAAE,IAAE,EAAE,IAAG,IAAE,IAAG,CAAC,CAAC,EAAE,GAAC,IAAE,IAAE,CAAC,IAAE,EAAE,IAAG,IAAE,KAAG,EAAE,IAAG,KAAG,CAAC,IAAE,IAAE,CAAC,GAAE,GAAE,OAAO,IAAG,KAAG,GAAE;YAAC,IAAI,IAAE,GAAE,IAAE,CAAC,CAAC,EAAE,EAAC,KAAG,IAAG,KAAG,GAAG;YAAI,IAAI,IAAE,CAAC,CAAC,EAAE,IAAE,GAAE,IAAE,GAAE,KAAG,IAAG,KAAG,GAAG;YAAI,KAAG,KAAG,CAAC,EAAE,CAAC,IAAG,CAAC,CAAC,EAAE,IAAE,KAAG,CAAC,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC;YAAE;QAAK,OAAK;YAAC,IAAG,CAAC,CAAC,EAAE,IAAE,GAAE,CAAC,CAAC,EAAE,IAAE,GAAE;YAAM,CAAC,CAAC,IAAI,GAAC,GAAE,IAAE;QAAC;QAAC,IAAI,IAAE,EAAE,MAAM,EAAC,CAAC,CAAC,EAAE,EAAE,KAAG,GAAG,EAAE,GAAG;IAAE;IAAC,OAAO,KAAG,CAAC,EAAE,CAAC,GAAC,EAAE,IAAI,GAAC,CAAC,EAAE,CAAC,GAAC,MAAK,EAAE,CAAC,GAAC,GAAG,IAAE,EAAE,CAAC,GAAC,EAAE,IAAI,IAAE,CAAC,EAAE,CAAC,GAAC,GAAE,EAAE,CAAC,GAAC;QAAC;KAAE,CAAC,GAAE;AAAC;AAAC,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,CAAC;IAAE,IAAG,CAAC,EAAE,QAAQ,IAAG,OAAO,GAAG;IAAG,IAAI,GAAE,IAAE,EAAE,CAAC,EAAC,IAAE,EAAE,EAAE,CAAC,GAAE,IAAE,EAAE,MAAM;IAAC,OAAO,IAAE,CAAC,KAAG,CAAC,IAAE,IAAE,CAAC,IAAE,IAAE,IAAE,EAAE,MAAM,CAAC,KAAG,MAAI,EAAE,KAAK,CAAC,KAAG,EAAE,KAAG,IAAE,KAAG,CAAC,IAAE,EAAE,MAAM,CAAC,KAAG,MAAI,EAAE,KAAK,CAAC,EAAE,GAAE,IAAE,IAAE,CAAC,EAAE,CAAC,GAAC,IAAE,MAAI,IAAI,IAAE,EAAE,CAAC,IAAE,IAAE,IAAE,CAAC,IAAE,OAAK,EAAE,CAAC,IAAE,KAAG,GAAE,KAAG,CAAC,IAAE,IAAE,CAAC,IAAE,KAAG,CAAC,KAAG,EAAE,EAAE,CAAC,IAAE,KAAG,IAAE,CAAC,KAAG,EAAE,IAAE,IAAE,IAAG,KAAG,CAAC,IAAE,IAAE,IAAE,CAAC,IAAE,KAAG,CAAC,IAAE,IAAE,MAAI,EAAE,EAAE,CAAC,IAAE,CAAC,CAAC,IAAE,IAAE,CAAC,IAAE,KAAG,CAAC,IAAE,EAAE,KAAK,CAAC,GAAE,KAAG,MAAI,EAAE,KAAK,CAAC,EAAE,GAAE,KAAG,CAAC,IAAE,IAAE,CAAC,IAAE,KAAG,CAAC,IAAE,MAAI,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,EAAE,EAAE,CAAC,GAAE;AAAC;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,CAAC,CAAC,EAAE;IAAC,IAAI,KAAG,GAAE,KAAG,IAAG,KAAG,GAAG;IAAI,OAAO;AAAC;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC,EAAC,CAAC;IAAE,IAAG,IAAE,IAAG,MAAM,IAAE,CAAC,GAAE,KAAG,CAAC,EAAE,SAAS,GAAC,CAAC,GAAE,MAAM;IAAI,OAAO,EAAE,IAAI,EAAE,KAAI,GAAE,GAAE,CAAC;AAAE;AAAC,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,CAAC;IAAE,IAAG,IAAE,IAAG,MAAM,MAAM;IAAI,OAAO,EAAE,IAAI,EAAE,KAAI,GAAE,GAAE,CAAC;AAAE;AAAC,SAAS,GAAG,CAAC;IAAE,IAAI,IAAE,EAAE,MAAM,GAAC,GAAE,IAAE,IAAE,IAAE;IAAE,IAAG,IAAE,CAAC,CAAC,EAAE,EAAC,GAAE;QAAC,MAAK,IAAE,MAAI,GAAE,KAAG,GAAG;QAAI,IAAI,IAAE,CAAC,CAAC,EAAE,EAAC,KAAG,IAAG,KAAG,GAAG;IAAG;IAAC,OAAO;AAAC;AAAC,SAAS,EAAE,CAAC;IAAE,IAAI,IAAI,IAAE,IAAG,KAAK,KAAG;IAAI,OAAO;AAAC;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;IAAE,IAAI,GAAE,IAAE,IAAI,EAAE,IAAG,IAAE,KAAK,IAAI,CAAC,IAAE,IAAE;IAAG,IAAI,IAAE,CAAC,IAAI;QAAC,IAAG,IAAE,KAAG,CAAC,IAAE,EAAE,KAAK,CAAC,IAAG,GAAG,EAAE,CAAC,EAAC,MAAI,CAAC,IAAE,CAAC,CAAC,CAAC,GAAE,IAAE,EAAE,IAAE,IAAG,MAAI,GAAE;YAAC,IAAE,EAAE,CAAC,CAAC,MAAM,GAAC,GAAE,KAAG,EAAE,CAAC,CAAC,EAAE,KAAG,KAAG,EAAE,EAAE,CAAC,CAAC,EAAE;YAAC;QAAK;QAAC,IAAE,EAAE,KAAK,CAAC,IAAG,GAAG,EAAE,CAAC,EAAC;IAAE;IAAC,OAAO,IAAE,CAAC,GAAE;AAAC;AAAC,SAAS,GAAG,CAAC;IAAE,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,GAAC,EAAE,GAAC;AAAC;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC,EAAC,CAAC;IAAE,IAAI,IAAI,GAAE,GAAE,IAAE,IAAI,EAAE,CAAC,CAAC,EAAE,GAAE,IAAE,GAAE,EAAE,IAAE,EAAE,MAAM,EAAE;QAAC,IAAG,IAAE,IAAI,EAAE,CAAC,CAAC,EAAE,GAAE,CAAC,EAAE,CAAC,EAAC;YAAC,IAAE;YAAE;QAAK;QAAC,IAAE,EAAE,GAAG,CAAC,IAAG,CAAC,MAAI,KAAG,MAAI,KAAG,EAAE,CAAC,KAAG,CAAC,KAAG,CAAC,IAAE,CAAC;IAAC;IAAC,OAAO;AAAC;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,EAAE,WAAW,EAAC,IAAE,EAAE,QAAQ,EAAC,IAAE,EAAE,SAAS;IAAC,IAAG,CAAC,EAAE,CAAC,IAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAE,EAAE,CAAC,GAAC,IAAG,OAAO,IAAI,EAAE,EAAE,CAAC,GAAC,EAAE,CAAC,CAAC,EAAE,GAAC,EAAE,CAAC,GAAC,IAAE,IAAE,IAAE,IAAE,IAAE,EAAE,CAAC,GAAC,EAAE,CAAC,GAAC,IAAE,IAAE,IAAE;IAAK,IAAI,KAAG,OAAK,CAAC,IAAE,CAAC,GAAE,IAAE,CAAC,IAAE,IAAE,GAAE,IAAE,IAAI,EAAE,SAAQ,EAAE,CAAC,GAAC,CAAC,GAAG,IAAE,EAAE,KAAK,CAAC,IAAG,KAAG;IAAE,IAAI,IAAE,KAAK,GAAG,CAAC,EAAE,GAAE,MAAI,KAAK,IAAI,GAAC,IAAE,IAAE,GAAE,KAAG,GAAE,IAAE,IAAE,IAAE,IAAI,EAAE,IAAG,EAAE,SAAS,GAAC,IAAI;QAAC,IAAG,IAAE,EAAE,EAAE,KAAK,CAAC,IAAG,GAAE,IAAG,IAAE,EAAE,KAAK,CAAC,EAAE,IAAG,IAAE,EAAE,IAAI,CAAC,EAAE,GAAE,GAAE,GAAE,KAAI,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,GAAE,OAAK,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,GAAE,IAAG;YAAC,IAAI,IAAE,GAAE,KAAK,IAAE,EAAE,EAAE,KAAK,CAAC,IAAG,GAAE;YAAG,IAAG,KAAG,MAAK,IAAG,IAAE,KAAG,EAAE,EAAE,CAAC,EAAC,IAAE,GAAE,GAAE,IAAG,EAAE,SAAS,GAAC,KAAG,IAAG,IAAE,IAAE,IAAE,IAAI,EAAE,IAAG,IAAE,GAAE;iBAAS,OAAO,EAAE,GAAE,EAAE,SAAS,GAAC,GAAE,GAAE,IAAE,CAAC;iBAAQ,OAAO,EAAE,SAAS,GAAC,GAAE;QAAC;QAAC,IAAE;IAAC;AAAC;AAAC,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,IAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAE,GAAE,IAAE,IAAG,IAAE,GAAE,IAAE,EAAE,CAAC,EAAC,IAAE,EAAE,WAAW,EAAC,IAAE,EAAE,QAAQ,EAAC,IAAE,EAAE,SAAS;IAAC,IAAG,EAAE,CAAC,GAAC,KAAG,CAAC,KAAG,CAAC,CAAC,CAAC,EAAE,IAAE,CAAC,EAAE,CAAC,IAAE,CAAC,CAAC,EAAE,IAAE,KAAG,EAAE,MAAM,IAAE,GAAE,OAAO,IAAI,EAAE,KAAG,CAAC,CAAC,CAAC,EAAE,GAAC,CAAC,IAAE,IAAE,EAAE,CAAC,IAAE,IAAE,MAAI,IAAE,IAAE;IAAG,IAAG,KAAG,OAAK,CAAC,IAAE,CAAC,GAAE,IAAE,CAAC,IAAE,IAAE,GAAE,EAAE,SAAS,GAAC,KAAG,GAAE,IAAE,EAAE,IAAG,IAAE,EAAE,MAAM,CAAC,IAAG,KAAK,GAAG,CAAC,IAAE,EAAE,CAAC,IAAE,OAAM;QAAC,MAAK,IAAE,KAAG,KAAG,KAAG,KAAG,KAAG,EAAE,MAAM,CAAC,KAAG,GAAG,IAAE,EAAE,KAAK,CAAC,IAAG,IAAE,EAAE,EAAE,CAAC,GAAE,IAAE,EAAE,MAAM,CAAC,IAAG;QAAI,IAAE,EAAE,CAAC,EAAC,IAAE,IAAE,CAAC,IAAE,IAAI,EAAE,OAAK,IAAG,GAAG,IAAE,IAAE,IAAI,EAAE,IAAE,MAAI,EAAE,KAAK,CAAC;IAAG,OAAM,OAAO,IAAE,GAAG,GAAE,IAAE,GAAE,GAAG,KAAK,CAAC,IAAE,KAAI,IAAE,EAAE,IAAI,EAAE,IAAE,MAAI,EAAE,KAAK,CAAC,KAAI,IAAE,GAAG,IAAI,CAAC,IAAG,EAAE,SAAS,GAAC,GAAE,KAAG,OAAK,EAAE,GAAE,GAAE,GAAE,IAAE,CAAC,KAAG;IAAE,IAAI,IAAE,GAAE,IAAE,IAAE,IAAE,EAAE,EAAE,KAAK,CAAC,IAAG,EAAE,IAAI,CAAC,IAAG,GAAE,IAAG,IAAE,EAAE,EAAE,KAAK,CAAC,IAAG,GAAE,IAAG,IAAE,IAAI;QAAC,IAAG,IAAE,EAAE,EAAE,KAAK,CAAC,IAAG,GAAE,IAAG,IAAE,EAAE,IAAI,CAAC,EAAE,GAAE,IAAI,EAAE,IAAG,GAAE,KAAI,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,GAAE,OAAK,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,GAAE,IAAG,IAAG,IAAE,EAAE,KAAK,CAAC,IAAG,MAAI,KAAG,CAAC,IAAE,EAAE,IAAI,CAAC,GAAG,GAAE,IAAE,GAAE,GAAG,KAAK,CAAC,IAAE,IAAI,GAAE,IAAE,EAAE,GAAE,IAAI,EAAE,IAAG,GAAE,IAAG,KAAG,MAAK,IAAG,EAAE,EAAE,CAAC,EAAC,IAAE,GAAE,GAAE,IAAG,EAAE,SAAS,GAAC,KAAG,GAAE,IAAE,IAAE,IAAE,EAAE,EAAE,KAAK,CAAC,IAAG,EAAE,IAAI,CAAC,IAAG,GAAE,IAAG,IAAE,EAAE,EAAE,KAAK,CAAC,IAAG,GAAE,IAAG,IAAE,IAAE;aAAO,OAAO,EAAE,GAAE,EAAE,SAAS,GAAC,GAAE,GAAE,IAAE,CAAC;aAAQ,OAAO,EAAE,SAAS,GAAC,GAAE;QAAE,IAAE,GAAE,KAAG;IAAC;AAAC;AAAC,SAAS,GAAG,CAAC;IAAE,OAAO,OAAO,EAAE,CAAC,GAAC,EAAE,CAAC,GAAC;AAAE;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAI,GAAE,GAAE;IAAE,IAAI,CAAC,IAAE,EAAE,OAAO,CAAC,IAAI,IAAE,CAAC,KAAG,CAAC,IAAE,EAAE,OAAO,CAAC,KAAI,GAAG,GAAE,CAAC,IAAE,EAAE,MAAM,CAAC,KAAK,IAAE,IAAE,CAAC,IAAE,KAAG,CAAC,IAAE,CAAC,GAAE,KAAG,CAAC,EAAE,KAAK,CAAC,IAAE,IAAG,IAAE,EAAE,SAAS,CAAC,GAAE,EAAE,IAAE,IAAE,KAAG,CAAC,IAAE,EAAE,MAAM,GAAE,IAAE,GAAE,EAAE,UAAU,CAAC,OAAK,IAAG;IAAK,IAAI,IAAE,EAAE,MAAM,EAAC,EAAE,UAAU,CAAC,IAAE,OAAK,IAAG,EAAE;IAAG,IAAG,IAAE,EAAE,KAAK,CAAC,GAAE,IAAG,GAAE;QAAC,IAAG,KAAG,GAAE,EAAE,CAAC,GAAC,IAAE,IAAE,IAAE,GAAE,EAAE,CAAC,GAAC,EAAE,EAAC,IAAE,CAAC,IAAE,CAAC,IAAE,GAAE,IAAE,KAAG,CAAC,KAAG,CAAC,GAAE,IAAE,GAAE;YAAC,IAAI,KAAG,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,GAAE,KAAI,KAAG,GAAE,IAAE,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,GAAE,KAAG;YAAI,IAAE,EAAE,KAAK,CAAC,IAAG,IAAE,IAAE,EAAE,MAAM;QAAA,OAAM,KAAG;QAAE,MAAK,KAAK,KAAG;QAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,IAAG,KAAG,CAAC,EAAE,CAAC,GAAC,EAAE,WAAW,CAAC,IAAI,GAAC,CAAC,EAAE,CAAC,GAAC,MAAK,EAAE,CAAC,GAAC,GAAG,IAAE,EAAE,CAAC,GAAC,EAAE,WAAW,CAAC,IAAI,IAAE,CAAC,EAAE,CAAC,GAAC,GAAE,EAAE,CAAC,GAAC;YAAC;SAAE,CAAC;IAAC,OAAM,EAAE,CAAC,GAAC,GAAE,EAAE,CAAC,GAAC;QAAC;KAAE;IAAC,OAAO;AAAC;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;IAAE,IAAG,EAAE,OAAO,CAAC,OAAK,CAAC,GAAE;QAAC,IAAG,IAAE,EAAE,OAAO,CAAC,gBAAe,OAAM,GAAG,IAAI,CAAC,IAAG,OAAO,GAAG,GAAE;IAAE,OAAM,IAAG,MAAI,cAAY,MAAI,OAAM,OAAM,CAAC,KAAG,CAAC,EAAE,CAAC,GAAC,GAAG,GAAE,EAAE,CAAC,GAAC,KAAI,EAAE,CAAC,GAAC,MAAK;IAAE,IAAG,GAAG,IAAI,CAAC,IAAG,IAAE,IAAG,IAAE,EAAE,WAAW;SAAQ,IAAG,GAAG,IAAI,CAAC,IAAG,IAAE;SAAO,IAAG,GAAG,IAAI,CAAC,IAAG,IAAE;SAAO,MAAM,MAAM,IAAE;IAAG,IAAI,IAAE,EAAE,MAAM,CAAC,OAAM,IAAE,IAAE,CAAC,IAAE,CAAC,EAAE,KAAK,CAAC,IAAE,IAAG,IAAE,EAAE,SAAS,CAAC,GAAE,EAAE,IAAE,IAAE,EAAE,KAAK,CAAC,IAAG,IAAE,EAAE,OAAO,CAAC,MAAK,IAAE,KAAG,GAAE,IAAE,EAAE,WAAW,EAAC,KAAG,CAAC,IAAE,EAAE,OAAO,CAAC,KAAI,KAAI,IAAE,EAAE,MAAM,EAAC,IAAE,IAAE,GAAE,IAAE,GAAG,GAAE,IAAI,EAAE,IAAG,GAAE,IAAE,EAAE,GAAE,IAAE,GAAG,GAAE,GAAE,IAAG,IAAE,EAAE,MAAM,GAAC,GAAE,IAAE,GAAE,CAAC,CAAC,EAAE,KAAG,GAAE,EAAE,EAAE,EAAE,GAAG;IAAG,OAAO,IAAE,IAAE,IAAI,EAAE,EAAE,CAAC,GAAC,KAAG,CAAC,EAAE,CAAC,GAAC,GAAG,GAAE,IAAG,EAAE,CAAC,GAAC,GAAE,IAAE,CAAC,GAAE,KAAG,CAAC,IAAE,EAAE,GAAE,GAAE,IAAE,EAAE,GAAE,KAAG,CAAC,IAAE,EAAE,KAAK,CAAC,KAAK,GAAG,CAAC,KAAG,KAAG,EAAE,GAAE,KAAG,EAAE,GAAG,CAAC,GAAE,GAAG,GAAE,IAAE,CAAC,GAAE,CAAC;AAAC;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAI,GAAE,IAAE,EAAE,CAAC,CAAC,MAAM;IAAC,IAAG,IAAE,GAAE,OAAO,EAAE,MAAM,KAAG,IAAE,EAAE,GAAE,GAAE,GAAE;IAAG,IAAE,MAAI,KAAK,IAAI,CAAC,IAAG,IAAE,IAAE,KAAG,KAAG,IAAE,GAAE,IAAE,EAAE,KAAK,CAAC,IAAE,GAAG,GAAE,KAAI,IAAE,EAAE,GAAE,GAAE,GAAE;IAAG,IAAI,IAAI,GAAE,IAAE,IAAI,EAAE,IAAG,IAAE,IAAI,EAAE,KAAI,IAAE,IAAI,EAAE,KAAI,KAAK,IAAE,EAAE,KAAK,CAAC,IAAG,IAAE,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC;IAAM,OAAO;AAAC;AAAC,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;IAAE,IAAI,GAAE,GAAE,GAAE,GAAE,IAAE,GAAE,IAAE,EAAE,SAAS,EAAC,IAAE,KAAK,IAAI,CAAC,IAAE;IAAG,IAAI,IAAE,CAAC,GAAE,IAAE,EAAE,KAAK,CAAC,IAAG,IAAE,IAAI,EAAE,KAAK;QAAC,IAAG,IAAE,EAAE,EAAE,KAAK,CAAC,IAAG,IAAI,EAAE,MAAI,MAAK,GAAE,IAAG,IAAE,IAAE,EAAE,IAAI,CAAC,KAAG,EAAE,KAAK,CAAC,IAAG,IAAE,EAAE,EAAE,KAAK,CAAC,IAAG,IAAI,EAAE,MAAI,MAAK,GAAE,IAAG,IAAE,EAAE,IAAI,CAAC,IAAG,EAAE,CAAC,CAAC,EAAE,KAAG,KAAK,GAAE;YAAC,IAAI,IAAE,GAAE,EAAE,CAAC,CAAC,EAAE,KAAG,EAAE,CAAC,CAAC,EAAE,IAAE;YAAM,IAAG,KAAG,CAAC,GAAE;QAAK;QAAC,IAAE,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE,GAAE;IAAG;IAAC,OAAO,IAAE,CAAC,GAAE,EAAE,CAAC,CAAC,MAAM,GAAC,IAAE,GAAE;AAAC;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAI,IAAI,IAAE,GAAE,EAAE,GAAG,KAAG;IAAE,OAAO;AAAC;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAI,GAAE,IAAE,EAAE,CAAC,GAAC,GAAE,IAAE,EAAE,GAAE,EAAE,SAAS,EAAC,IAAG,IAAE,EAAE,KAAK,CAAC;IAAI,IAAG,IAAE,EAAE,GAAG,IAAG,EAAE,GAAG,CAAC,IAAG,OAAO,IAAE,IAAE,IAAE,GAAE;IAAE,IAAG,IAAE,EAAE,QAAQ,CAAC,IAAG,EAAE,MAAM,IAAG,IAAE,IAAE,IAAE;SAAM;QAAC,IAAG,IAAE,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,KAAI,EAAE,GAAG,CAAC,IAAG,OAAO,IAAE,GAAG,KAAG,IAAE,IAAE,IAAE,IAAE,IAAE,GAAE;QAAE,IAAE,GAAG,KAAG,IAAE,IAAE,IAAE,IAAE,IAAE;IAAC;IAAC,OAAO,EAAE,KAAK,CAAC,GAAG,GAAG;AAAE;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;IAAE,IAAI,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAE,EAAE,WAAW,EAAC,IAAE,MAAI,KAAK;IAAE,IAAG,IAAE,CAAC,EAAE,GAAE,GAAE,IAAG,MAAI,KAAK,IAAE,IAAE,EAAE,QAAQ,GAAC,EAAE,GAAE,GAAE,EAAE,IAAE,CAAC,IAAE,EAAE,SAAS,EAAC,IAAE,EAAE,QAAQ,GAAE,CAAC,EAAE,QAAQ,IAAG,IAAE,GAAG;SAAO;QAAC,IAAI,IAAE,EAAE,IAAG,IAAE,EAAE,OAAO,CAAC,MAAK,IAAE,CAAC,IAAE,GAAE,KAAG,KAAG,IAAE,IAAE,IAAE,IAAE,KAAG,KAAG,CAAC,IAAE,IAAE,IAAE,CAAC,CAAC,IAAE,IAAE,GAAE,KAAG,KAAG,CAAC,IAAE,EAAE,OAAO,CAAC,KAAI,KAAI,IAAE,IAAI,EAAE,IAAG,EAAE,CAAC,GAAC,EAAE,MAAM,GAAC,GAAE,EAAE,CAAC,GAAC,GAAG,EAAE,IAAG,IAAG,IAAG,EAAE,CAAC,GAAC,EAAE,CAAC,CAAC,MAAM,GAAE,IAAE,GAAG,GAAE,IAAG,IAAG,IAAE,IAAE,EAAE,MAAM,EAAC,CAAC,CAAC,EAAE,EAAE,IAAE,GAAG,EAAE,GAAG;QAAG,IAAG,CAAC,CAAC,CAAC,EAAE,EAAC,IAAE,IAAE,SAAO;aAAQ;YAAC,IAAG,IAAE,IAAE,MAAI,CAAC,IAAE,IAAI,EAAE,IAAG,EAAE,CAAC,GAAC,GAAE,EAAE,CAAC,GAAC,GAAE,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,GAAE,IAAG,IAAE,EAAE,CAAC,EAAC,IAAE,EAAE,CAAC,EAAC,IAAE,EAAE,GAAE,IAAE,CAAC,CAAC,EAAE,EAAC,IAAE,IAAE,GAAE,IAAE,KAAG,CAAC,CAAC,IAAE,EAAE,KAAG,KAAK,GAAE,IAAE,IAAE,IAAE,CAAC,MAAI,KAAK,KAAG,CAAC,KAAG,CAAC,MAAI,KAAG,MAAI,CAAC,EAAE,CAAC,GAAC,IAAE,IAAE,CAAC,CAAC,IAAE,IAAE,KAAG,MAAI,KAAG,CAAC,MAAI,KAAG,KAAG,MAAI,KAAG,CAAC,CAAC,IAAE,EAAE,GAAC,KAAG,MAAI,CAAC,EAAE,CAAC,GAAC,IAAE,IAAE,CAAC,CAAC,GAAE,EAAE,MAAM,GAAC,GAAE,GAAE,MAAK,EAAE,CAAC,CAAC,EAAE,EAAE,GAAC,IAAE,GAAG,CAAC,CAAC,EAAE,GAAC,GAAE,KAAG,CAAC,EAAE,GAAE,EAAE,OAAO,CAAC,EAAE;YAAE,IAAI,IAAE,EAAE,MAAM,EAAC,CAAC,CAAC,CAAC,IAAE,EAAE,EAAC,EAAE;YAAG,IAAI,IAAE,GAAE,IAAE,IAAG,IAAE,GAAE,IAAI,KAAG,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE;YAAE,IAAG,GAAE;gBAAC,IAAG,IAAE,GAAE,IAAG,KAAG,MAAI,KAAG,GAAE;oBAAC,IAAI,IAAE,KAAG,KAAG,IAAE,GAAE,EAAE,GAAE,IAAE,GAAE,IAAI,KAAG;oBAAI,IAAI,IAAE,GAAG,GAAE,GAAE,IAAG,IAAE,EAAE,MAAM,EAAC,CAAC,CAAC,CAAC,IAAE,EAAE,EAAC,EAAE;oBAAG,IAAI,IAAE,GAAE,IAAE,MAAK,IAAE,GAAE,IAAI,KAAG,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE;gBAAC,OAAM,IAAE,EAAE,MAAM,CAAC,KAAG,MAAI,EAAE,KAAK,CAAC;gBAAG,IAAE,IAAE,CAAC,IAAE,IAAE,MAAI,IAAI,IAAE;YAAC,OAAM,IAAG,IAAE,GAAE;gBAAC,MAAK,EAAE,GAAG,IAAE,MAAI;gBAAE,IAAE,OAAK;YAAC,OAAM,IAAG,EAAE,IAAE,GAAE,IAAI,KAAG,GAAE,KAAK,KAAG;iBAAS,IAAE,KAAG,CAAC,IAAE,EAAE,KAAK,CAAC,GAAE,KAAG,MAAI,EAAE,KAAK,CAAC,EAAE;QAAC;QAAC,IAAE,CAAC,KAAG,KAAG,OAAK,KAAG,IAAE,OAAK,KAAG,IAAE,OAAK,EAAE,IAAE;IAAC;IAAC,OAAO,EAAE,CAAC,GAAC,IAAE,MAAI,IAAE;AAAC;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAG,EAAE,MAAM,GAAC,GAAE,OAAO,EAAE,MAAM,GAAC,GAAE,CAAC;AAAC;AAAC,SAAS,GAAG,CAAC;IAAE,OAAO,IAAI,IAAI,CAAC,GAAG,GAAG;AAAE;AAAC,SAAS,GAAG,CAAC;IAAE,OAAO,IAAI,IAAI,CAAC,GAAG,IAAI;AAAE;AAAC,SAAS,GAAG,CAAC;IAAE,OAAO,IAAI,IAAI,CAAC,GAAG,KAAK;AAAE;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,OAAO,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC;AAAE;AAAC,SAAS,GAAG,CAAC;IAAE,OAAO,IAAI,IAAI,CAAC,GAAG,IAAI;AAAE;AAAC,SAAS,GAAG,CAAC;IAAE,OAAO,IAAI,IAAI,CAAC,GAAG,KAAK;AAAE;AAAC,SAAS,GAAG,CAAC;IAAE,OAAO,IAAI,IAAI,CAAC,GAAG,IAAI;AAAE;AAAC,SAAS,GAAG,CAAC;IAAE,OAAO,IAAI,IAAI,CAAC,GAAG,KAAK;AAAE;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,IAAE,IAAI,IAAI,CAAC,IAAG,IAAE,IAAI,IAAI,CAAC;IAAG,IAAI,GAAE,IAAE,IAAI,CAAC,SAAS,EAAC,IAAE,IAAI,CAAC,QAAQ,EAAC,IAAE,IAAE;IAAE,OAAM,CAAC,EAAE,CAAC,IAAE,CAAC,EAAE,CAAC,GAAC,IAAE,IAAI,IAAI,CAAC,OAAK,CAAC,EAAE,CAAC,IAAE,CAAC,EAAE,CAAC,GAAC,CAAC,IAAE,EAAE,IAAI,EAAC,GAAE,GAAG,KAAK,CAAC,EAAE,CAAC,GAAC,IAAE,MAAI,MAAK,EAAE,CAAC,GAAC,EAAE,CAAC,IAAE,CAAC,EAAE,CAAC,IAAE,EAAE,MAAM,KAAG,CAAC,IAAE,EAAE,CAAC,GAAC,IAAE,EAAE,IAAI,EAAC,GAAE,KAAG,IAAI,IAAI,CAAC,IAAG,EAAE,CAAC,GAAC,EAAE,CAAC,IAAE,CAAC,EAAE,CAAC,IAAE,EAAE,MAAM,KAAG,CAAC,IAAE,EAAE,IAAI,EAAC,GAAE,GAAG,KAAK,CAAC,KAAI,EAAE,CAAC,GAAC,EAAE,CAAC,IAAE,EAAE,CAAC,GAAC,IAAE,CAAC,IAAI,CAAC,SAAS,GAAC,GAAE,IAAI,CAAC,QAAQ,GAAC,GAAE,IAAE,IAAI,CAAC,IAAI,CAAC,EAAE,GAAE,GAAE,GAAE,KAAI,IAAE,EAAE,IAAI,EAAC,GAAE,IAAG,IAAI,CAAC,SAAS,GAAC,GAAE,IAAI,CAAC,QAAQ,GAAC,GAAE,IAAE,EAAE,CAAC,GAAC,IAAE,EAAE,KAAK,CAAC,KAAG,EAAE,IAAI,CAAC,EAAE,IAAE,IAAE,IAAI,CAAC,IAAI,CAAC,EAAE,GAAE,GAAE,GAAE,KAAI;AAAC;AAAC,SAAS,GAAG,CAAC;IAAE,OAAO,IAAI,IAAI,CAAC,GAAG,IAAI;AAAE;AAAC,SAAS,GAAG,CAAC;IAAE,OAAO,EAAE,IAAE,IAAI,IAAI,CAAC,IAAG,EAAE,CAAC,GAAC,GAAE;AAAE;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC,EAAC,CAAC;IAAE,OAAO,IAAI,IAAI,CAAC,GAAG,KAAK,CAAC,GAAE;AAAE;AAAC,SAAS,GAAG,CAAC;IAAE,IAAG,CAAC,KAAG,OAAO,KAAG,UAAS,MAAM,MAAM,KAAG;IAAmB,IAAI,GAAE,GAAE,GAAE,IAAE,EAAE,QAAQ,KAAG,CAAC,GAAE,IAAE;QAAC;QAAY;QAAE;QAAE;QAAW;QAAE;QAAE;QAAW,CAAC;QAAE;QAAE;QAAW;QAAE;QAAE;QAAO;QAAE;QAAE;QAAO,CAAC;QAAE;QAAE;QAAS;QAAE;KAAE;IAAC,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,KAAG,EAAE,IAAG,IAAE,CAAC,CAAC,EAAE,EAAC,KAAG,CAAC,IAAI,CAAC,EAAE,GAAC,EAAE,CAAC,EAAE,GAAE,CAAC,IAAE,CAAC,CAAC,EAAE,MAAI,KAAK,GAAE,IAAG,EAAE,OAAK,KAAG,KAAG,CAAC,CAAC,IAAE,EAAE,IAAE,KAAG,CAAC,CAAC,IAAE,EAAE,EAAC,IAAI,CAAC,EAAE,GAAC;SAAO,MAAM,MAAM,IAAE,IAAE,OAAK;IAAG,IAAG,IAAE,UAAS,KAAG,CAAC,IAAI,CAAC,EAAE,GAAC,EAAE,CAAC,EAAE,GAAE,CAAC,IAAE,CAAC,CAAC,EAAE,MAAI,KAAK,GAAE,IAAG,MAAI,CAAC,KAAG,MAAI,CAAC,KAAG,MAAI,KAAG,MAAI,GAAE,IAAG,GAAE,IAAG,OAAO,SAAO,OAAK,UAAQ,CAAC,OAAO,eAAe,IAAE,OAAO,WAAW,GAAE,IAAI,CAAC,EAAE,GAAC,CAAC;SAAO,MAAM,MAAM;SAAS,IAAI,CAAC,EAAE,GAAC,CAAC;SAAO,MAAM,MAAM,IAAE,IAAE,OAAK;IAAG,OAAO,IAAI;AAAA;AAAC,SAAS,GAAG,CAAC;IAAE,OAAO,IAAI,IAAI,CAAC,GAAG,GAAG;AAAE;AAAC,SAAS,GAAG,CAAC;IAAE,OAAO,IAAI,IAAI,CAAC,GAAG,IAAI;AAAE;AAAC,SAAS,GAAG,CAAC;IAAE,IAAI,GAAE,GAAE;IAAE,SAAS,EAAE,CAAC;QAAE,IAAI,GAAE,GAAE,GAAE,IAAE,IAAI;QAAC,IAAG,CAAC,CAAC,aAAa,CAAC,GAAE,OAAO,IAAI,EAAE;QAAG,IAAG,EAAE,WAAW,GAAC,GAAE,GAAG,IAAG;YAAC,EAAE,CAAC,GAAC,EAAE,CAAC,EAAC,IAAE,CAAC,EAAE,CAAC,IAAE,EAAE,CAAC,GAAC,EAAE,IAAI,GAAC,CAAC,EAAE,CAAC,GAAC,KAAI,EAAE,CAAC,GAAC,IAAI,IAAE,EAAE,CAAC,GAAC,EAAE,IAAI,GAAC,CAAC,EAAE,CAAC,GAAC,GAAE,EAAE,CAAC,GAAC;gBAAC;aAAE,IAAE,CAAC,EAAE,CAAC,GAAC,EAAE,CAAC,EAAC,EAAE,CAAC,GAAC,EAAE,CAAC,CAAC,KAAK,EAAE,IAAE,CAAC,EAAE,CAAC,GAAC,EAAE,CAAC,EAAC,EAAE,CAAC,GAAC,EAAE,CAAC,GAAC,EAAE,CAAC,CAAC,KAAK,KAAG,EAAE,CAAC;YAAE;QAAM;QAAC,IAAG,IAAE,OAAO,GAAE,MAAI,UAAS;YAAC,IAAG,MAAI,GAAE;gBAAC,EAAE,CAAC,GAAC,IAAE,IAAE,IAAE,CAAC,IAAE,GAAE,EAAE,CAAC,GAAC,GAAE,EAAE,CAAC,GAAC;oBAAC;iBAAE;gBAAC;YAAM;YAAC,IAAG,IAAE,IAAE,CAAC,IAAE,CAAC,GAAE,EAAE,CAAC,GAAC,CAAC,CAAC,IAAE,EAAE,CAAC,GAAC,GAAE,MAAI,CAAC,CAAC,KAAG,IAAE,KAAI;gBAAC,IAAI,IAAE,GAAE,IAAE,GAAE,KAAG,IAAG,KAAG,GAAG;gBAAI,IAAE,IAAE,EAAE,IAAI,GAAC,CAAC,EAAE,CAAC,GAAC,KAAI,EAAE,CAAC,GAAC,IAAI,IAAE,IAAE,EAAE,IAAI,GAAC,CAAC,EAAE,CAAC,GAAC,GAAE,EAAE,CAAC,GAAC;oBAAC;iBAAE,IAAE,CAAC,EAAE,CAAC,GAAC,GAAE,EAAE,CAAC,GAAC;oBAAC;iBAAE,IAAE,CAAC,EAAE,CAAC,GAAC,GAAE,EAAE,CAAC,GAAC;oBAAC;iBAAE;gBAAE;YAAM;YAAC,IAAG,IAAE,MAAI,GAAE;gBAAC,KAAG,CAAC,EAAE,CAAC,GAAC,GAAG,GAAE,EAAE,CAAC,GAAC,KAAI,EAAE,CAAC,GAAC;gBAAK;YAAM;YAAC,OAAO,GAAG,GAAE,EAAE,QAAQ;QAAG;QAAC,IAAG,MAAI,UAAS,OAAM,CAAC,IAAE,EAAE,UAAU,CAAC,EAAE,MAAI,KAAG,CAAC,IAAE,EAAE,KAAK,CAAC,IAAG,EAAE,CAAC,GAAC,CAAC,CAAC,IAAE,CAAC,MAAI,MAAI,CAAC,IAAE,EAAE,KAAK,CAAC,EAAE,GAAE,EAAE,CAAC,GAAC,CAAC,GAAE,GAAG,IAAI,CAAC,KAAG,GAAG,GAAE,KAAG,GAAG,GAAE;QAAG,IAAG,MAAI,UAAS,OAAO,IAAE,IAAE,CAAC,IAAE,CAAC,GAAE,EAAE,CAAC,GAAC,CAAC,CAAC,IAAE,EAAE,CAAC,GAAC,GAAE,GAAG,GAAE,EAAE,QAAQ;QAAI,MAAM,MAAM,IAAE;IAAE;IAAC,IAAG,EAAE,SAAS,GAAC,GAAE,EAAE,QAAQ,GAAC,GAAE,EAAE,UAAU,GAAC,GAAE,EAAE,UAAU,GAAC,GAAE,EAAE,WAAW,GAAC,GAAE,EAAE,aAAa,GAAC,GAAE,EAAE,eAAe,GAAC,GAAE,EAAE,eAAe,GAAC,GAAE,EAAE,eAAe,GAAC,GAAE,EAAE,gBAAgB,GAAC,GAAE,EAAE,MAAM,GAAC,GAAE,EAAE,MAAM,GAAC,EAAE,GAAG,GAAC,IAAG,EAAE,KAAK,GAAC,IAAG,EAAE,SAAS,GAAC,IAAG,EAAE,GAAG,GAAC,IAAG,EAAE,IAAI,GAAC,IAAG,EAAE,KAAK,GAAC,IAAG,EAAE,GAAG,GAAC,IAAG,EAAE,IAAI,GAAC,IAAG,EAAE,KAAK,GAAC,IAAG,EAAE,IAAI,GAAC,IAAG,EAAE,KAAK,GAAC,IAAG,EAAE,KAAK,GAAC,IAAG,EAAE,IAAI,GAAC,IAAG,EAAE,IAAI,GAAC,IAAG,EAAE,KAAK,GAAC,IAAG,EAAE,GAAG,GAAC,IAAG,EAAE,IAAI,GAAC,IAAG,EAAE,GAAG,GAAC,IAAG,EAAE,GAAG,GAAC,IAAG,EAAE,KAAK,GAAC,IAAG,EAAE,KAAK,GAAC,IAAG,EAAE,EAAE,GAAC,IAAG,EAAE,GAAG,GAAC,IAAG,EAAE,KAAK,GAAC,IAAG,EAAE,IAAI,GAAC,IAAG,EAAE,GAAG,GAAC,IAAG,EAAE,GAAG,GAAC,IAAG,EAAE,GAAG,GAAC,IAAG,EAAE,GAAG,GAAC,IAAG,EAAE,GAAG,GAAC,IAAG,EAAE,MAAM,GAAC,IAAG,EAAE,KAAK,GAAC,IAAG,EAAE,IAAI,GAAC,IAAG,EAAE,GAAG,GAAC,IAAG,EAAE,IAAI,GAAC,IAAG,EAAE,IAAI,GAAC,IAAG,EAAE,GAAG,GAAC,IAAG,EAAE,GAAG,GAAC,IAAG,EAAE,GAAG,GAAC,IAAG,EAAE,IAAI,GAAC,IAAG,EAAE,KAAK,GAAC,IAAG,MAAI,KAAK,KAAG,CAAC,IAAE,CAAC,CAAC,GAAE,KAAG,EAAE,QAAQ,KAAG,CAAC,GAAE,IAAI,IAAE;QAAC;QAAY;QAAW;QAAW;QAAW;QAAO;QAAO;QAAS;KAAS,EAAC,IAAE,GAAE,IAAE,EAAE,MAAM,EAAE,EAAE,cAAc,CAAC,IAAE,CAAC,CAAC,IAAI,KAAG,CAAC,CAAC,CAAC,EAAE,GAAC,IAAI,CAAC,EAAE;IAAE,OAAO,EAAE,MAAM,CAAC,IAAG;AAAC;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,OAAO,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC;AAAE;AAAC,SAAS,GAAG,CAAC;IAAE,OAAO,IAAI,IAAI,CAAC,GAAG,GAAG;AAAE;AAAC,SAAS,GAAG,CAAC;IAAE,OAAO,EAAE,IAAE,IAAI,IAAI,CAAC,IAAG,EAAE,CAAC,GAAC,GAAE;AAAE;AAAC,SAAS;IAAK,IAAI,GAAE,GAAE,IAAE,IAAI,IAAI,CAAC;IAAG,IAAI,IAAE,CAAC,GAAE,IAAE,GAAE,IAAE,UAAU,MAAM,EAAE,IAAG,IAAE,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,GAAE,EAAE,CAAC,EAAC,EAAE,CAAC,IAAE,CAAC,IAAE,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC,GAAG;SAAM;QAAC,IAAG,EAAE,CAAC,EAAC,OAAO,IAAE,CAAC,GAAE,IAAI,IAAI,CAAC,IAAE;QAAG,IAAE;IAAC;IAAC,OAAO,IAAE,CAAC,GAAE,EAAE,IAAI;AAAE;AAAC,SAAS,GAAG,CAAC;IAAE,OAAO,aAAa,KAAG,KAAG,EAAE,WAAW,KAAG,MAAI,CAAC;AAAC;AAAC,SAAS,GAAG,CAAC;IAAE,OAAO,IAAI,IAAI,CAAC,GAAG,EAAE;AAAE;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,OAAO,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC;AAAE;AAAC,SAAS,GAAG,CAAC;IAAE,OAAO,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC;AAAE;AAAC,SAAS,GAAG,CAAC;IAAE,OAAO,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC;AAAG;AAAC,SAAS;IAAK,OAAO,GAAG,IAAI,EAAC,WAAU,CAAC;AAAE;AAAC,SAAS;IAAK,OAAO,GAAG,IAAI,EAAC,WAAU;AAAE;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,OAAO,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC;AAAE;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,OAAO,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC;AAAE;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,OAAO,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC;AAAE;AAAC,SAAS,GAAG,CAAC;IAAE,IAAI,GAAE,GAAE,GAAE,GAAE,IAAE,GAAE,IAAE,IAAI,IAAI,CAAC,IAAG,IAAE,EAAE;IAAC,IAAG,MAAI,KAAK,IAAE,IAAE,IAAI,CAAC,SAAS,GAAC,EAAE,GAAE,GAAE,IAAG,IAAE,KAAK,IAAI,CAAC,IAAE,IAAG,IAAI,CAAC,MAAM,EAAC,IAAG,OAAO,eAAe,EAAC,IAAI,IAAE,OAAO,eAAe,CAAC,IAAI,YAAY,KAAI,IAAE,GAAG,IAAE,CAAC,CAAC,EAAE,EAAC,KAAG,QAAM,CAAC,CAAC,EAAE,GAAC,OAAO,eAAe,CAAC,IAAI,YAAY,GAAG,CAAC,EAAE,GAAC,CAAC,CAAC,IAAI,GAAC,IAAE;SAAS,IAAG,OAAO,WAAW,EAAC;QAAC,IAAI,IAAE,OAAO,WAAW,CAAC,KAAG,IAAG,IAAE,GAAG,IAAE,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,CAAC,IAAE,EAAE,IAAE,CAAC,IAAE,CAAC,CAAC,CAAC,IAAE,EAAE,IAAE,EAAE,IAAE,CAAC,CAAC,CAAC,CAAC,IAAE,EAAE,GAAC,GAAG,KAAG,EAAE,GAAE,KAAG,QAAM,OAAO,WAAW,CAAC,GAAG,IAAI,CAAC,GAAE,KAAG,CAAC,EAAE,IAAI,CAAC,IAAE,MAAK,KAAG,CAAC;QAAE,IAAE,IAAE;IAAC,OAAM,MAAM,MAAM;SAAS,MAAK,IAAE,GAAG,CAAC,CAAC,IAAI,GAAC,KAAK,MAAM,KAAG,MAAI;IAAE,IAAI,IAAE,CAAC,CAAC,EAAE,EAAE,EAAC,KAAG,GAAE,KAAG,KAAG,CAAC,IAAE,EAAE,IAAG,IAAE,IAAG,CAAC,CAAC,EAAE,GAAC,CAAC,IAAE,IAAE,CAAC,IAAE,CAAC,GAAE,CAAC,CAAC,EAAE,KAAG,GAAE,IAAI,EAAE,GAAG;IAAG,IAAG,IAAE,GAAE,IAAE,GAAE,IAAE;QAAC;KAAE;SAAK;QAAC,IAAI,IAAE,CAAC,GAAE,CAAC,CAAC,EAAE,KAAG,GAAE,KAAG,EAAE,EAAE,KAAK;QAAG,IAAI,IAAE,GAAE,IAAE,CAAC,CAAC,EAAE,EAAC,KAAG,IAAG,KAAG,GAAG;QAAI,IAAE,KAAG,CAAC,KAAG,IAAE,CAAC;IAAC;IAAC,OAAO,EAAE,CAAC,GAAC,GAAE,EAAE,CAAC,GAAC,GAAE;AAAC;AAAC,SAAS,GAAG,CAAC;IAAE,OAAO,EAAE,IAAE,IAAI,IAAI,CAAC,IAAG,EAAE,CAAC,GAAC,GAAE,IAAI,CAAC,QAAQ;AAAC;AAAC,SAAS,GAAG,CAAC;IAAE,OAAO,IAAE,IAAI,IAAI,CAAC,IAAG,EAAE,CAAC,GAAC,EAAE,CAAC,CAAC,EAAE,GAAC,EAAE,CAAC,GAAC,IAAE,EAAE,CAAC,GAAC,EAAE,CAAC,IAAE;AAAG;AAAC,SAAS,GAAG,CAAC;IAAE,OAAO,IAAI,IAAI,CAAC,GAAG,GAAG;AAAE;AAAC,SAAS,GAAG,CAAC;IAAE,OAAO,IAAI,IAAI,CAAC,GAAG,IAAI;AAAE;AAAC,SAAS,GAAG,CAAC;IAAE,OAAO,IAAI,IAAI,CAAC,GAAG,IAAI;AAAE;AAAC,SAAS,GAAG,CAAC,EAAC,CAAC;IAAE,OAAO,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC;AAAE;AAAC,SAAS;IAAK,IAAI,IAAE,GAAE,IAAE,WAAU,IAAE,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE;IAAE,IAAI,IAAE,CAAC,GAAE,EAAE,CAAC,IAAE,EAAE,IAAE,EAAE,MAAM,EAAE,IAAE,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE;IAAE,OAAO,IAAE,CAAC,GAAE,EAAE,GAAE,IAAI,CAAC,SAAS,EAAC,IAAI,CAAC,QAAQ;AAAC;AAAC,SAAS,GAAG,CAAC;IAAE,OAAO,IAAI,IAAI,CAAC,GAAG,GAAG;AAAE;AAAC,SAAS,GAAG,CAAC;IAAE,OAAO,IAAI,IAAI,CAAC,GAAG,IAAI;AAAE;AAAC,SAAS,GAAG,CAAC;IAAE,OAAO,EAAE,IAAE,IAAI,IAAI,CAAC,IAAG,EAAE,CAAC,GAAC,GAAE;AAAE;AAAC,CAAC,CAAC,OAAO,GAAG,CAAC,8BAA8B,GAAC,EAAE,QAAQ;AAAC,CAAC,CAAC,OAAO,WAAW,CAAC,GAAC;AAAU,IAAI,IAAE,EAAE,WAAW,GAAC,GAAG;AAAI,KAAG,IAAI,EAAE;AAAI,KAAG,IAAI,EAAE;AAAI,IAAI,KAAG;AAAE,KAAG,CAAC,OAAO,OAAO,GAAC;IAAC;IAAQ;IAAO;IAAW;IAAe;AAAgB,CAAC,GAC7rkC;;;;;;;;;;AAUA,IACA,yCAAyC"}}, {"offset": {"line": 1109, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/lib/generated/prisma/index-browser.js"], "sourcesContent": ["\n/* !!! This is code generated by Prisma. Do not edit directly. !!!\n/* eslint-disable */\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\n\nconst {\n  Decimal,\n  objectEnumValues,\n  makeStrictEnum,\n  Public,\n  getRuntime,\n  skip\n} = require('./runtime/index-browser.js')\n\n\nconst Prisma = {}\n\nexports.Prisma = Prisma\nexports.$Enums = {}\n\n/**\n * Prisma Client JS version: 6.13.0\n * Query Engine version: 361e86d0ea4987e9f53a565309b3eed797a6bcbd\n */\nPrisma.prismaVersion = {\n  client: \"6.13.0\",\n  engine: \"361e86d0ea4987e9f53a565309b3eed797a6bcbd\"\n}\n\nPrisma.PrismaClientKnownRequestError = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)};\nPrisma.PrismaClientUnknownRequestError = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.PrismaClientRustPanicError = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.PrismaClientInitializationError = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.PrismaClientValidationError = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.Decimal = Decimal\n\n/**\n * Re-export of sql-template-tag\n */\nPrisma.sql = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.empty = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.join = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.raw = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.validator = Public.validator\n\n/**\n* Extensions\n*/\nPrisma.getExtensionContext = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.defineExtension = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\n\n/**\n * Shorthand utilities for JSON filtering\n */\nPrisma.DbNull = objectEnumValues.instances.DbNull\nPrisma.JsonNull = objectEnumValues.instances.JsonNull\nPrisma.AnyNull = objectEnumValues.instances.AnyNull\n\nPrisma.NullTypes = {\n  DbNull: objectEnumValues.classes.DbNull,\n  JsonNull: objectEnumValues.classes.JsonNull,\n  AnyNull: objectEnumValues.classes.AnyNull\n}\n\n\n\n/**\n * Enums\n */\n\nexports.Prisma.TransactionIsolationLevel = makeStrictEnum({\n  Serializable: 'Serializable'\n});\n\nexports.Prisma.UserScalarFieldEnum = {\n  id: 'id',\n  username: 'username',\n  email: 'email',\n  passwordHash: 'passwordHash',\n  firstName: 'firstName',\n  lastName: 'lastName',\n  middleName: 'middleName',\n  role: 'role',\n  permissions: 'permissions',\n  isActive: 'isActive',\n  lastLoginAt: 'lastLoginAt',\n  lastLoginIp: 'lastLoginIp',\n  failedLoginAttempts: 'failedLoginAttempts',\n  lockedUntil: 'lockedUntil',\n  passwordChangedAt: 'passwordChangedAt',\n  mustChangePassword: 'mustChangePassword',\n  twoFactorEnabled: 'twoFactorEnabled',\n  twoFactorSecret: 'twoFactorSecret',\n  sessionToken: 'sessionToken',\n  sessionExpiresAt: 'sessionExpiresAt',\n  phoneNumber: 'phoneNumber',\n  department: 'department',\n  position: 'position',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  createdBy: 'createdBy',\n  teacherId: 'teacherId'\n};\n\nexports.Prisma.LoginHistoryScalarFieldEnum = {\n  id: 'id',\n  userId: 'userId',\n  loginAt: 'loginAt',\n  ipAddress: 'ipAddress',\n  userAgent: 'userAgent',\n  location: 'location',\n  success: 'success',\n  failureReason: 'failureReason',\n  sessionId: 'sessionId',\n  sessionDuration: 'sessionDuration',\n  logoutAt: 'logoutAt',\n  logoutReason: 'logoutReason',\n  deviceType: 'deviceType',\n  browser: 'browser',\n  operatingSystem: 'operatingSystem',\n  createdAt: 'createdAt'\n};\n\nexports.Prisma.SecurityEventScalarFieldEnum = {\n  id: 'id',\n  userId: 'userId',\n  eventType: 'eventType',\n  severity: 'severity',\n  description: 'description',\n  ipAddress: 'ipAddress',\n  userAgent: 'userAgent',\n  additionalData: 'additionalData',\n  resolved: 'resolved',\n  resolvedAt: 'resolvedAt',\n  resolvedBy: 'resolvedBy',\n  resolution: 'resolution',\n  createdAt: 'createdAt'\n};\n\nexports.Prisma.PermissionScalarFieldEnum = {\n  id: 'id',\n  name: 'name',\n  description: 'description',\n  category: 'category',\n  resource: 'resource',\n  action: 'action',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt'\n};\n\nexports.Prisma.RolePermissionScalarFieldEnum = {\n  id: 'id',\n  role: 'role',\n  permissionId: 'permissionId',\n  conditions: 'conditions',\n  createdAt: 'createdAt'\n};\n\nexports.Prisma.StudentScalarFieldEnum = {\n  id: 'id',\n  studentId: 'studentId',\n  firstName: 'firstName',\n  lastName: 'lastName',\n  middleName: 'middleName',\n  email: 'email',\n  dateOfBirth: 'dateOfBirth',\n  gender: 'gender',\n  gradeLevel: 'gradeLevel',\n  section: 'section',\n  course: 'course',\n  year: 'year',\n  status: 'status',\n  enrollmentDate: 'enrollmentDate',\n  guardianName: 'guardianName',\n  guardianPhone: 'guardianPhone',\n  guardianEmail: 'guardianEmail',\n  guardianRelationship: 'guardianRelationship',\n  emergencyContactName: 'emergencyContactName',\n  emergencyContactPhone: 'emergencyContactPhone',\n  emergencyContactRelationship: 'emergencyContactRelationship',\n  address: 'address',\n  barangay: 'barangay',\n  municipality: 'municipality',\n  province: 'province',\n  zipCode: 'zipCode',\n  photoUrl: 'photoUrl',\n  qrCodeData: 'qrCodeData',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt'\n};\n\nexports.Prisma.TeacherScalarFieldEnum = {\n  id: 'id',\n  employeeId: 'employeeId',\n  firstName: 'firstName',\n  lastName: 'lastName',\n  middleName: 'middleName',\n  email: 'email',\n  phoneNumber: 'phoneNumber',\n  role: 'role',\n  subjects: 'subjects',\n  gradeLevels: 'gradeLevels',\n  sections: 'sections',\n  status: 'status',\n  hireDate: 'hireDate',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt'\n};\n\nexports.Prisma.SubjectScalarFieldEnum = {\n  id: 'id',\n  subjectCode: 'subjectCode',\n  subjectName: 'subjectName',\n  gradeLevel: 'gradeLevel',\n  credits: 'credits',\n  room: 'room',\n  schedule: 'schedule',\n  teacherId: 'teacherId',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt'\n};\n\nexports.Prisma.AttendanceScalarFieldEnum = {\n  id: 'id',\n  studentId: 'studentId',\n  date: 'date',\n  timeIn: 'timeIn',\n  timeOut: 'timeOut',\n  attendanceType: 'attendanceType',\n  subjectId: 'subjectId',\n  status: 'status',\n  scannedBy: 'scannedBy',\n  notes: 'notes',\n  isManualEntry: 'isManualEntry',\n  location: 'location',\n  deviceId: 'deviceId',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt'\n};\n\nexports.Prisma.AttendancePatternScalarFieldEnum = {\n  id: 'id',\n  studentId: 'studentId',\n  weeklyRate: 'weeklyRate',\n  monthlyRate: 'monthlyRate',\n  consecutiveAbsences: 'consecutiveAbsences',\n  latePattern: 'latePattern',\n  riskScore: 'riskScore',\n  riskLevel: 'riskLevel',\n  predictions: 'predictions',\n  insights: 'insights',\n  lastAnalyzed: 'lastAnalyzed',\n  dataPoints: 'dataPoints',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt'\n};\n\nexports.Prisma.StudentRiskAssessmentScalarFieldEnum = {\n  id: 'id',\n  studentId: 'studentId',\n  riskScore: 'riskScore',\n  riskLevel: 'riskLevel',\n  dropoutProbability: 'dropoutProbability',\n  riskFactors: 'riskFactors',\n  interventions: 'interventions',\n  parentEngagementScore: 'parentEngagementScore',\n  lastParentContact: 'lastParentContact',\n  parentResponseRate: 'parentResponseRate',\n  assessmentDate: 'assessmentDate',\n  nextReviewDate: 'nextReviewDate',\n  assessedBy: 'assessedBy',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt'\n};\n\nexports.Prisma.SMSLogScalarFieldEnum = {\n  id: 'id',\n  recipientPhone: 'recipientPhone',\n  message: 'message',\n  status: 'status',\n  sentAt: 'sentAt',\n  deliveredAt: 'deliveredAt',\n  errorMessage: 'errorMessage',\n  relatedStudentId: 'relatedStudentId',\n  messageType: 'messageType',\n  provider: 'provider',\n  messageId: 'messageId',\n  cost: 'cost',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt'\n};\n\nexports.Prisma.SystemSettingsScalarFieldEnum = {\n  id: 'id',\n  key: 'key',\n  value: 'value',\n  description: 'description',\n  category: 'category',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt'\n};\n\nexports.Prisma.AuditLogScalarFieldEnum = {\n  id: 'id',\n  userId: 'userId',\n  action: 'action',\n  entityType: 'entityType',\n  entityId: 'entityId',\n  oldValues: 'oldValues',\n  newValues: 'newValues',\n  ipAddress: 'ipAddress',\n  userAgent: 'userAgent',\n  createdAt: 'createdAt'\n};\n\nexports.Prisma.NotificationQueueScalarFieldEnum = {\n  id: 'id',\n  recipientId: 'recipientId',\n  recipientType: 'recipientType',\n  type: 'type',\n  title: 'title',\n  message: 'message',\n  priority: 'priority',\n  channels: 'channels',\n  status: 'status',\n  scheduledFor: 'scheduledFor',\n  sentAt: 'sentAt',\n  deliveredAt: 'deliveredAt',\n  failureReason: 'failureReason',\n  retryCount: 'retryCount',\n  maxRetries: 'maxRetries',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt'\n};\n\nexports.Prisma.SortOrder = {\n  asc: 'asc',\n  desc: 'desc'\n};\n\nexports.Prisma.NullsOrder = {\n  first: 'first',\n  last: 'last'\n};\nexports.UserRole = exports.$Enums.UserRole = {\n  ADMIN: 'ADMIN',\n  PRINCIPAL: 'PRINCIPAL',\n  TEACHER: 'TEACHER',\n  SCANNER: 'SCANNER'\n};\n\nexports.SecurityEventType = exports.$Enums.SecurityEventType = {\n  LOGIN_SUCCESS: 'LOGIN_SUCCESS',\n  LOGIN_FAILURE: 'LOGIN_FAILURE',\n  LOGOUT: 'LOGOUT',\n  PASSWORD_CHANGE: 'PASSWORD_CHANGE',\n  ACCOUNT_LOCKED: 'ACCOUNT_LOCKED',\n  ACCOUNT_UNLOCKED: 'ACCOUNT_UNLOCKED',\n  PERMISSION_DENIED: 'PERMISSION_DENIED',\n  SUSPICIOUS_ACTIVITY: 'SUSPICIOUS_ACTIVITY',\n  DATA_EXPORT: 'DATA_EXPORT',\n  BULK_OPERATION: 'BULK_OPERATION',\n  SYSTEM_ACCESS: 'SYSTEM_ACCESS'\n};\n\nexports.SecuritySeverity = exports.$Enums.SecuritySeverity = {\n  LOW: 'LOW',\n  MEDIUM: 'MEDIUM',\n  HIGH: 'HIGH',\n  CRITICAL: 'CRITICAL'\n};\n\nexports.Gender = exports.$Enums.Gender = {\n  MALE: 'MALE',\n  FEMALE: 'FEMALE'\n};\n\nexports.GradeLevel = exports.$Enums.GradeLevel = {\n  GRADE_7: 'GRADE_7',\n  GRADE_8: 'GRADE_8',\n  GRADE_9: 'GRADE_9',\n  GRADE_10: 'GRADE_10',\n  GRADE_11: 'GRADE_11',\n  GRADE_12: 'GRADE_12'\n};\n\nexports.StudentStatus = exports.$Enums.StudentStatus = {\n  ACTIVE: 'ACTIVE',\n  INACTIVE: 'INACTIVE',\n  TRANSFERRED: 'TRANSFERRED',\n  GRADUATED: 'GRADUATED',\n  DROPPED: 'DROPPED'\n};\n\nexports.GuardianRelationship = exports.$Enums.GuardianRelationship = {\n  FATHER: 'FATHER',\n  MOTHER: 'MOTHER',\n  GUARDIAN: 'GUARDIAN',\n  GRANDPARENT: 'GRANDPARENT',\n  SIBLING: 'SIBLING',\n  AUNT: 'AUNT',\n  UNCLE: 'UNCLE',\n  OTHER: 'OTHER'\n};\n\nexports.TeacherRole = exports.$Enums.TeacherRole = {\n  TEACHER: 'TEACHER',\n  DEPARTMENT_HEAD: 'DEPARTMENT_HEAD',\n  GUIDANCE_COUNSELOR: 'GUIDANCE_COUNSELOR',\n  ADMIN: 'ADMIN',\n  PRINCIPAL: 'PRINCIPAL',\n  VICE_PRINCIPAL: 'VICE_PRINCIPAL'\n};\n\nexports.EmployeeStatus = exports.$Enums.EmployeeStatus = {\n  ACTIVE: 'ACTIVE',\n  INACTIVE: 'INACTIVE',\n  RESIGNED: 'RESIGNED',\n  TERMINATED: 'TERMINATED',\n  RETIRED: 'RETIRED'\n};\n\nexports.AttendanceType = exports.$Enums.AttendanceType = {\n  GATE: 'GATE',\n  SUBJECT: 'SUBJECT',\n  EVENT: 'EVENT',\n  ASSEMBLY: 'ASSEMBLY'\n};\n\nexports.AttendanceStatus = exports.$Enums.AttendanceStatus = {\n  PRESENT: 'PRESENT',\n  ABSENT: 'ABSENT',\n  LATE: 'LATE',\n  EXCUSED: 'EXCUSED',\n  HALF_DAY: 'HALF_DAY'\n};\n\nexports.RiskLevel = exports.$Enums.RiskLevel = {\n  LOW: 'LOW',\n  MEDIUM: 'MEDIUM',\n  HIGH: 'HIGH',\n  CRITICAL: 'CRITICAL'\n};\n\nexports.SMSStatus = exports.$Enums.SMSStatus = {\n  PENDING: 'PENDING',\n  SENT: 'SENT',\n  DELIVERED: 'DELIVERED',\n  FAILED: 'FAILED',\n  EXPIRED: 'EXPIRED'\n};\n\nexports.MessageType = exports.$Enums.MessageType = {\n  ATTENDANCE_ALERT: 'ATTENDANCE_ALERT',\n  LATE_ARRIVAL: 'LATE_ARRIVAL',\n  RISK_WARNING: 'RISK_WARNING',\n  GENERAL: 'GENERAL',\n  EMERGENCY: 'EMERGENCY',\n  PARENT_MEETING: 'PARENT_MEETING',\n  ACADEMIC_UPDATE: 'ACADEMIC_UPDATE'\n};\n\nexports.NotificationPriority = exports.$Enums.NotificationPriority = {\n  LOW: 'LOW',\n  NORMAL: 'NORMAL',\n  HIGH: 'HIGH',\n  URGENT: 'URGENT'\n};\n\nexports.NotificationStatus = exports.$Enums.NotificationStatus = {\n  PENDING: 'PENDING',\n  SCHEDULED: 'SCHEDULED',\n  SENT: 'SENT',\n  DELIVERED: 'DELIVERED',\n  FAILED: 'FAILED',\n  CANCELLED: 'CANCELLED'\n};\n\nexports.Prisma.ModelName = {\n  User: 'User',\n  LoginHistory: 'LoginHistory',\n  SecurityEvent: 'SecurityEvent',\n  Permission: 'Permission',\n  RolePermission: 'RolePermission',\n  Student: 'Student',\n  Teacher: 'Teacher',\n  Subject: 'Subject',\n  Attendance: 'Attendance',\n  AttendancePattern: 'AttendancePattern',\n  StudentRiskAssessment: 'StudentRiskAssessment',\n  SMSLog: 'SMSLog',\n  SystemSettings: 'SystemSettings',\n  AuditLog: 'AuditLog',\n  NotificationQueue: 'NotificationQueue'\n};\n\n/**\n * This is a stub Prisma Client that will error at runtime if called.\n */\nclass PrismaClient {\n  constructor() {\n    return new Proxy(this, {\n      get(target, prop) {\n        let message\n        const runtime = getRuntime()\n        if (runtime.isEdge) {\n          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:\n- Use Prisma Accelerate: https://pris.ly/d/accelerate\n- Use Driver Adapters: https://pris.ly/d/driver-adapters\n`;\n        } else {\n          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'\n        }\n\n        message += `\nIf this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`\n\n        throw new Error(message)\n      }\n    })\n  }\n}\n\nexports.PrismaClient = PrismaClient\n\nObject.assign(exports, Prisma)\n"], "names": [], "mappings": "AACA;kBACkB,GAElB,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAE3D,MAAM,EACJ,OAAO,EACP,gBAAgB,EAChB,cAAc,EACd,MAAM,EACN,UAAU,EACV,IAAI,EACL;AAGD,MAAM,SAAS,CAAC;AAEhB,QAAQ,MAAM,GAAG;AACjB,QAAQ,MAAM,GAAG,CAAC;AAElB;;;CAGC,GACD,OAAO,aAAa,GAAG;IACrB,QAAQ;IACR,QAAQ;AACV;AAEA,OAAO,6BAA6B,GAAG;IACrC,MAAM,cAAc,aAAa,UAAU;IAC3C,MAAM,IAAI,MAAM,CAAC,4HAA4H,EAAE,YAAY;sGACvD,CAAC;AACtG;AACD,OAAO,+BAA+B,GAAG;IACvC,MAAM,cAAc,aAAa,UAAU;IAC3C,MAAM,IAAI,MAAM,CAAC,8HAA8H,EAAE,YAAY;sGACzD,CAAC;AACtG;AACD,OAAO,0BAA0B,GAAG;IAClC,MAAM,cAAc,aAAa,UAAU;IAC3C,MAAM,IAAI,MAAM,CAAC,yHAAyH,EAAE,YAAY;sGACpD,CAAC;AACtG;AACD,OAAO,+BAA+B,GAAG;IACvC,MAAM,cAAc,aAAa,UAAU;IAC3C,MAAM,IAAI,MAAM,CAAC,8HAA8H,EAAE,YAAY;sGACzD,CAAC;AACtG;AACD,OAAO,2BAA2B,GAAG;IACnC,MAAM,cAAc,aAAa,UAAU;IAC3C,MAAM,IAAI,MAAM,CAAC,0HAA0H,EAAE,YAAY;sGACrD,CAAC;AACtG;AACD,OAAO,OAAO,GAAG;AAEjB;;CAEC,GACD,OAAO,GAAG,GAAG;IACX,MAAM,cAAc,aAAa,UAAU;IAC3C,MAAM,IAAI,MAAM,CAAC,qGAAqG,EAAE,YAAY;sGAChC,CAAC;AACtG;AACD,OAAO,KAAK,GAAG;IACb,MAAM,cAAc,aAAa,UAAU;IAC3C,MAAM,IAAI,MAAM,CAAC,oGAAoG,EAAE,YAAY;sGAC/B,CAAC;AACtG;AACD,OAAO,IAAI,GAAG;IACZ,MAAM,cAAc,aAAa,UAAU;IAC3C,MAAM,IAAI,MAAM,CAAC,mGAAmG,EAAE,YAAY;sGAC9B,CAAC;AACtG;AACD,OAAO,GAAG,GAAG;IACX,MAAM,cAAc,aAAa,UAAU;IAC3C,MAAM,IAAI,MAAM,CAAC,kGAAkG,EAAE,YAAY;sGAC7B,CAAC;AACtG;AACD,OAAO,SAAS,GAAG,OAAO,SAAS;AAEnC;;AAEA,GACA,OAAO,mBAAmB,GAAG;IAC3B,MAAM,cAAc,aAAa,UAAU;IAC3C,MAAM,IAAI,MAAM,CAAC,6HAA6H,EAAE,YAAY;sGACxD,CAAC;AACtG;AACD,OAAO,eAAe,GAAG;IACvB,MAAM,cAAc,aAAa,UAAU;IAC3C,MAAM,IAAI,MAAM,CAAC,yHAAyH,EAAE,YAAY;sGACpD,CAAC;AACtG;AAED;;CAEC,GACD,OAAO,MAAM,GAAG,iBAAiB,SAAS,CAAC,MAAM;AACjD,OAAO,QAAQ,GAAG,iBAAiB,SAAS,CAAC,QAAQ;AACrD,OAAO,OAAO,GAAG,iBAAiB,SAAS,CAAC,OAAO;AAEnD,OAAO,SAAS,GAAG;IACjB,QAAQ,iBAAiB,OAAO,CAAC,MAAM;IACvC,UAAU,iBAAiB,OAAO,CAAC,QAAQ;IAC3C,SAAS,iBAAiB,OAAO,CAAC,OAAO;AAC3C;AAIA;;CAEC,GAED,QAAQ,MAAM,CAAC,yBAAyB,GAAG,eAAe;IACxD,cAAc;AAChB;AAEA,QAAQ,MAAM,CAAC,mBAAmB,GAAG;IACnC,IAAI;IACJ,UAAU;IACV,OAAO;IACP,cAAc;IACd,WAAW;IACX,UAAU;IACV,YAAY;IACZ,MAAM;IACN,aAAa;IACb,UAAU;IACV,aAAa;IACb,aAAa;IACb,qBAAqB;IACrB,aAAa;IACb,mBAAmB;IACnB,oBAAoB;IACpB,kBAAkB;IAClB,iBAAiB;IACjB,cAAc;IACd,kBAAkB;IAClB,aAAa;IACb,YAAY;IACZ,UAAU;IACV,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;AACb;AAEA,QAAQ,MAAM,CAAC,2BAA2B,GAAG;IAC3C,IAAI;IACJ,QAAQ;IACR,SAAS;IACT,WAAW;IACX,WAAW;IACX,UAAU;IACV,SAAS;IACT,eAAe;IACf,WAAW;IACX,iBAAiB;IACjB,UAAU;IACV,cAAc;IACd,YAAY;IACZ,SAAS;IACT,iBAAiB;IACjB,WAAW;AACb;AAEA,QAAQ,MAAM,CAAC,4BAA4B,GAAG;IAC5C,IAAI;IACJ,QAAQ;IACR,WAAW;IACX,UAAU;IACV,aAAa;IACb,WAAW;IACX,WAAW;IACX,gBAAgB;IAChB,UAAU;IACV,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,WAAW;AACb;AAEA,QAAQ,MAAM,CAAC,yBAAyB,GAAG;IACzC,IAAI;IACJ,MAAM;IACN,aAAa;IACb,UAAU;IACV,UAAU;IACV,QAAQ;IACR,WAAW;IACX,WAAW;AACb;AAEA,QAAQ,MAAM,CAAC,6BAA6B,GAAG;IAC7C,IAAI;IACJ,MAAM;IACN,cAAc;IACd,YAAY;IACZ,WAAW;AACb;AAEA,QAAQ,MAAM,CAAC,sBAAsB,GAAG;IACtC,IAAI;IACJ,WAAW;IACX,WAAW;IACX,UAAU;IACV,YAAY;IACZ,OAAO;IACP,aAAa;IACb,QAAQ;IACR,YAAY;IACZ,SAAS;IACT,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,gBAAgB;IAChB,cAAc;IACd,eAAe;IACf,eAAe;IACf,sBAAsB;IACtB,sBAAsB;IACtB,uBAAuB;IACvB,8BAA8B;IAC9B,SAAS;IACT,UAAU;IACV,cAAc;IACd,UAAU;IACV,SAAS;IACT,UAAU;IACV,YAAY;IACZ,WAAW;IACX,WAAW;AACb;AAEA,QAAQ,MAAM,CAAC,sBAAsB,GAAG;IACtC,IAAI;IACJ,YAAY;IACZ,WAAW;IACX,UAAU;IACV,YAAY;IACZ,OAAO;IACP,aAAa;IACb,MAAM;IACN,UAAU;IACV,aAAa;IACb,UAAU;IACV,QAAQ;IACR,UAAU;IACV,WAAW;IACX,WAAW;AACb;AAEA,QAAQ,MAAM,CAAC,sBAAsB,GAAG;IACtC,IAAI;IACJ,aAAa;IACb,aAAa;IACb,YAAY;IACZ,SAAS;IACT,MAAM;IACN,UAAU;IACV,WAAW;IACX,WAAW;IACX,WAAW;AACb;AAEA,QAAQ,MAAM,CAAC,yBAAyB,GAAG;IACzC,IAAI;IACJ,WAAW;IACX,MAAM;IACN,QAAQ;IACR,SAAS;IACT,gBAAgB;IAChB,WAAW;IACX,QAAQ;IACR,WAAW;IACX,OAAO;IACP,eAAe;IACf,UAAU;IACV,UAAU;IACV,WAAW;IACX,WAAW;AACb;AAEA,QAAQ,MAAM,CAAC,gCAAgC,GAAG;IAChD,IAAI;IACJ,WAAW;IACX,YAAY;IACZ,aAAa;IACb,qBAAqB;IACrB,aAAa;IACb,WAAW;IACX,WAAW;IACX,aAAa;IACb,UAAU;IACV,cAAc;IACd,YAAY;IACZ,WAAW;IACX,WAAW;AACb;AAEA,QAAQ,MAAM,CAAC,oCAAoC,GAAG;IACpD,IAAI;IACJ,WAAW;IACX,WAAW;IACX,WAAW;IACX,oBAAoB;IACpB,aAAa;IACb,eAAe;IACf,uBAAuB;IACvB,mBAAmB;IACnB,oBAAoB;IACpB,gBAAgB;IAChB,gBAAgB;IAChB,YAAY;IACZ,WAAW;IACX,WAAW;AACb;AAEA,QAAQ,MAAM,CAAC,qBAAqB,GAAG;IACrC,IAAI;IACJ,gBAAgB;IAChB,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,aAAa;IACb,cAAc;IACd,kBAAkB;IAClB,aAAa;IACb,UAAU;IACV,WAAW;IACX,MAAM;IACN,WAAW;IACX,WAAW;AACb;AAEA,QAAQ,MAAM,CAAC,6BAA6B,GAAG;IAC7C,IAAI;IACJ,KAAK;IACL,OAAO;IACP,aAAa;IACb,UAAU;IACV,WAAW;IACX,WAAW;AACb;AAEA,QAAQ,MAAM,CAAC,uBAAuB,GAAG;IACvC,IAAI;IACJ,QAAQ;IACR,QAAQ;IACR,YAAY;IACZ,UAAU;IACV,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;AACb;AAEA,QAAQ,MAAM,CAAC,gCAAgC,GAAG;IAChD,IAAI;IACJ,aAAa;IACb,eAAe;IACf,MAAM;IACN,OAAO;IACP,SAAS;IACT,UAAU;IACV,UAAU;IACV,QAAQ;IACR,cAAc;IACd,QAAQ;IACR,aAAa;IACb,eAAe;IACf,YAAY;IACZ,YAAY;IACZ,WAAW;IACX,WAAW;AACb;AAEA,QAAQ,MAAM,CAAC,SAAS,GAAG;IACzB,KAAK;IACL,MAAM;AACR;AAEA,QAAQ,MAAM,CAAC,UAAU,GAAG;IAC1B,OAAO;IACP,MAAM;AACR;AACA,QAAQ,QAAQ,GAAG,QAAQ,MAAM,CAAC,QAAQ,GAAG;IAC3C,OAAO;IACP,WAAW;IACX,SAAS;IACT,SAAS;AACX;AAEA,QAAQ,iBAAiB,GAAG,QAAQ,MAAM,CAAC,iBAAiB,GAAG;IAC7D,eAAe;IACf,eAAe;IACf,QAAQ;IACR,iBAAiB;IACjB,gBAAgB;IAChB,kBAAkB;IAClB,mBAAmB;IACnB,qBAAqB;IACrB,aAAa;IACb,gBAAgB;IAChB,eAAe;AACjB;AAEA,QAAQ,gBAAgB,GAAG,QAAQ,MAAM,CAAC,gBAAgB,GAAG;IAC3D,KAAK;IACL,QAAQ;IACR,MAAM;IACN,UAAU;AACZ;AAEA,QAAQ,MAAM,GAAG,QAAQ,MAAM,CAAC,MAAM,GAAG;IACvC,MAAM;IACN,QAAQ;AACV;AAEA,QAAQ,UAAU,GAAG,QAAQ,MAAM,CAAC,UAAU,GAAG;IAC/C,SAAS;IACT,SAAS;IACT,SAAS;IACT,UAAU;IACV,UAAU;IACV,UAAU;AACZ;AAEA,QAAQ,aAAa,GAAG,QAAQ,MAAM,CAAC,aAAa,GAAG;IACrD,QAAQ;IACR,UAAU;IACV,aAAa;IACb,WAAW;IACX,SAAS;AACX;AAEA,QAAQ,oBAAoB,GAAG,QAAQ,MAAM,CAAC,oBAAoB,GAAG;IACnE,QAAQ;IACR,QAAQ;IACR,UAAU;IACV,aAAa;IACb,SAAS;IACT,MAAM;IACN,OAAO;IACP,OAAO;AACT;AAEA,QAAQ,WAAW,GAAG,QAAQ,MAAM,CAAC,WAAW,GAAG;IACjD,SAAS;IACT,iBAAiB;IACjB,oBAAoB;IACpB,OAAO;IACP,WAAW;IACX,gBAAgB;AAClB;AAEA,QAAQ,cAAc,GAAG,QAAQ,MAAM,CAAC,cAAc,GAAG;IACvD,QAAQ;IACR,UAAU;IACV,UAAU;IACV,YAAY;IACZ,SAAS;AACX;AAEA,QAAQ,cAAc,GAAG,QAAQ,MAAM,CAAC,cAAc,GAAG;IACvD,MAAM;IACN,SAAS;IACT,OAAO;IACP,UAAU;AACZ;AAEA,QAAQ,gBAAgB,GAAG,QAAQ,MAAM,CAAC,gBAAgB,GAAG;IAC3D,SAAS;IACT,QAAQ;IACR,MAAM;IACN,SAAS;IACT,UAAU;AACZ;AAEA,QAAQ,SAAS,GAAG,QAAQ,MAAM,CAAC,SAAS,GAAG;IAC7C,KAAK;IACL,QAAQ;IACR,MAAM;IACN,UAAU;AACZ;AAEA,QAAQ,SAAS,GAAG,QAAQ,MAAM,CAAC,SAAS,GAAG;IAC7C,SAAS;IACT,MAAM;IACN,WAAW;IACX,QAAQ;IACR,SAAS;AACX;AAEA,QAAQ,WAAW,GAAG,QAAQ,MAAM,CAAC,WAAW,GAAG;IACjD,kBAAkB;IAClB,cAAc;IACd,cAAc;IACd,SAAS;IACT,WAAW;IACX,gBAAgB;IAChB,iBAAiB;AACnB;AAEA,QAAQ,oBAAoB,GAAG,QAAQ,MAAM,CAAC,oBAAoB,GAAG;IACnE,KAAK;IACL,QAAQ;IACR,MAAM;IACN,QAAQ;AACV;AAEA,QAAQ,kBAAkB,GAAG,QAAQ,MAAM,CAAC,kBAAkB,GAAG;IAC/D,SAAS;IACT,WAAW;IACX,MAAM;IACN,WAAW;IACX,QAAQ;IACR,WAAW;AACb;AAEA,QAAQ,MAAM,CAAC,SAAS,GAAG;IACzB,MAAM;IACN,cAAc;IACd,eAAe;IACf,YAAY;IACZ,gBAAgB;IAChB,SAAS;IACT,SAAS;IACT,SAAS;IACT,YAAY;IACZ,mBAAmB;IACnB,uBAAuB;IACvB,QAAQ;IACR,gBAAgB;IAChB,UAAU;IACV,mBAAmB;AACrB;AAEA;;CAEC,GACD,MAAM;IACJ,aAAc;QACZ,OAAO,IAAI,MAAM,IAAI,EAAE;YACrB,KAAI,MAAM,EAAE,IAAI;gBACd,IAAI;gBACJ,MAAM,UAAU;gBAChB,IAAI,QAAQ,MAAM,EAAE;oBAClB,UAAU,CAAC,yCAAyC,EAAE,QAAQ,UAAU,CAAC;;;AAGnF,CAAC;gBACO,OAAO;oBACL,UAAU,iHAAiH,QAAQ,UAAU,GAAG;gBAClJ;gBAEA,WAAW,CAAC;qFACiE,CAAC;gBAE9E,MAAM,IAAI,MAAM;YAClB;QACF;IACF;AACF;AAEA,QAAQ,YAAY,GAAG;AAEvB,OAAO,MAAM,CAAC,SAAS"}}, {"offset": {"line": 1618, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from './generated/prisma'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,sJAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG"}}, {"offset": {"line": 1635, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/ (unsupported edge import path)"], "sourcesContent": ["__turbopack_context__.n(__import_unsupported(`path`));\n"], "names": [], "mappings": "AAAA,sBAAsB,CAAC,CAAC,qBAAqB,CAAC,IAAI,CAAC"}}, {"offset": {"line": 1641, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/ (unsupported edge import fs)"], "sourcesContent": ["__turbopack_context__.n(__import_unsupported(`fs`));\n"], "names": [], "mappings": "AAAA,sBAAsB,CAAC,CAAC,qBAAqB,CAAC,EAAE,CAAC"}}, {"offset": {"line": 1647, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/ (unsupported edge import os)"], "sourcesContent": ["__turbopack_context__.n(__import_unsupported(`os`));\n"], "names": [], "mappings": "AAAA,sBAAsB,CAAC,CAAC,qBAAqB,CAAC,EAAE,CAAC"}}, {"offset": {"line": 1653, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/ (unsupported edge import crypto)"], "sourcesContent": ["__turbopack_context__.n(__import_unsupported(`crypto`));\n"], "names": [], "mappings": "AAAA,sBAAsB,CAAC,CAAC,qBAAqB,CAAC,MAAM,CAAC"}}, {"offset": {"line": 1658, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/lib/auth/password.ts"], "sourcesContent": ["import bcrypt from 'bcrypt'\nimport { PasswordValidation } from './types'\n\n// Password security configuration\nconst SALT_ROUNDS = 12\nconst MIN_PASSWORD_LENGTH = 8\nconst MAX_PASSWORD_LENGTH = 128\n\n// Password complexity requirements\nconst PASSWORD_REQUIREMENTS = {\n  minLength: MIN_PASSWORD_LENGTH,\n  maxLength: MAX_PASSWORD_LENGTH,\n  requireUppercase: true,\n  requireLowercase: true,\n  requireNumbers: true,\n  requireSpecialChars: true,\n  minSpecialChars: 1,\n  preventCommonPasswords: true,\n  preventUserInfoInPassword: true\n}\n\n// Common weak passwords to reject\nconst COMMON_PASSWORDS = [\n  'password', '123456', '123456789', 'qwerty', 'abc123', 'password123',\n  'admin', 'letmein', 'welcome', 'monkey', '1234567890', 'password1',\n  'admin123', 'root', 'toor', 'pass', 'test', 'guest', 'user', 'demo'\n]\n\n/**\n * Hash a password using bcrypt\n */\nexport async function hashPassword(password: string): Promise<string> {\n  if (!password || password.length === 0) {\n    throw new Error('Password cannot be empty')\n  }\n  \n  if (password.length > MAX_PASSWORD_LENGTH) {\n    throw new Error(`Password cannot exceed ${MAX_PASSWORD_LENGTH} characters`)\n  }\n  \n  return await bcrypt.hash(password, SALT_ROUNDS)\n}\n\n/**\n * Verify a password against its hash\n */\nexport async function verifyPassword(password: string, hash: string): Promise<boolean> {\n  if (!password || !hash) {\n    return false\n  }\n  \n  try {\n    return await bcrypt.compare(password, hash)\n  } catch (error) {\n    console.error('Password verification error:', error)\n    return false\n  }\n}\n\n/**\n * Validate password strength and complexity\n */\nexport function validatePassword(\n  password: string, \n  userInfo?: { \n    username?: string\n    email?: string\n    firstName?: string\n    lastName?: string\n  }\n): PasswordValidation {\n  const errors: string[] = []\n  let score = 0\n  \n  // Check length\n  if (!password || password.length < PASSWORD_REQUIREMENTS.minLength) {\n    errors.push(`Password must be at least ${PASSWORD_REQUIREMENTS.minLength} characters long`)\n  } else {\n    score += 1\n  }\n  \n  if (password.length > PASSWORD_REQUIREMENTS.maxLength) {\n    errors.push(`Password cannot exceed ${PASSWORD_REQUIREMENTS.maxLength} characters`)\n  }\n  \n  // Check for uppercase letters\n  if (PASSWORD_REQUIREMENTS.requireUppercase && !/[A-Z]/.test(password)) {\n    errors.push('Password must contain at least one uppercase letter')\n  } else if (/[A-Z]/.test(password)) {\n    score += 1\n  }\n  \n  // Check for lowercase letters\n  if (PASSWORD_REQUIREMENTS.requireLowercase && !/[a-z]/.test(password)) {\n    errors.push('Password must contain at least one lowercase letter')\n  } else if (/[a-z]/.test(password)) {\n    score += 1\n  }\n  \n  // Check for numbers\n  if (PASSWORD_REQUIREMENTS.requireNumbers && !/\\d/.test(password)) {\n    errors.push('Password must contain at least one number')\n  } else if (/\\d/.test(password)) {\n    score += 1\n  }\n  \n  // Check for special characters\n  const specialChars = /[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?~`]/g\n  const specialCharCount = (password.match(specialChars) || []).length\n  \n  if (PASSWORD_REQUIREMENTS.requireSpecialChars && specialCharCount === 0) {\n    errors.push('Password must contain at least one special character (!@#$%^&*()_+-=[]{}|;:,.<>?)')\n  } else if (specialCharCount >= PASSWORD_REQUIREMENTS.minSpecialChars) {\n    score += 1\n  }\n  \n  // Check against common passwords\n  if (PASSWORD_REQUIREMENTS.preventCommonPasswords) {\n    const lowerPassword = password.toLowerCase()\n    if (COMMON_PASSWORDS.includes(lowerPassword)) {\n      errors.push('Password is too common. Please choose a more secure password')\n    }\n  }\n  \n  // Check if password contains user information\n  if (PASSWORD_REQUIREMENTS.preventUserInfoInPassword && userInfo) {\n    const lowerPassword = password.toLowerCase()\n    const userFields = [\n      userInfo.username?.toLowerCase(),\n      userInfo.email?.toLowerCase().split('@')[0],\n      userInfo.firstName?.toLowerCase(),\n      userInfo.lastName?.toLowerCase()\n    ].filter(Boolean)\n    \n    for (const field of userFields) {\n      if (field && field.length > 2 && lowerPassword.includes(field)) {\n        errors.push('Password cannot contain your personal information')\n        break\n      }\n    }\n  }\n  \n  // Additional scoring for length and complexity\n  if (password.length >= 12) score += 1\n  if (password.length >= 16) score += 1\n  if (specialCharCount >= 2) score += 1\n  if (/[A-Z].*[A-Z]/.test(password)) score += 1 // Multiple uppercase\n  if (/\\d.*\\d/.test(password)) score += 1 // Multiple numbers\n  \n  // Determine strength\n  let strength: 'weak' | 'medium' | 'strong'\n  if (score <= 3) {\n    strength = 'weak'\n  } else if (score <= 6) {\n    strength = 'medium'\n  } else {\n    strength = 'strong'\n  }\n  \n  return {\n    isValid: errors.length === 0,\n    errors,\n    strength,\n    score: Math.min(score, 10) // Cap at 10\n  }\n}\n\n/**\n * Generate a secure temporary password\n */\nexport function generateTemporaryPassword(length: number = 12): string {\n  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'\n  const lowercase = 'abcdefghijklmnopqrstuvwxyz'\n  const numbers = '0123456789'\n  const specialChars = '!@#$%^&*()_+-=[]{}|;:,.<>?'\n  \n  const allChars = uppercase + lowercase + numbers + specialChars\n  \n  let password = ''\n  \n  // Ensure at least one character from each required category\n  password += uppercase[Math.floor(Math.random() * uppercase.length)]\n  password += lowercase[Math.floor(Math.random() * lowercase.length)]\n  password += numbers[Math.floor(Math.random() * numbers.length)]\n  password += specialChars[Math.floor(Math.random() * specialChars.length)]\n  \n  // Fill the rest randomly\n  for (let i = password.length; i < length; i++) {\n    password += allChars[Math.floor(Math.random() * allChars.length)]\n  }\n  \n  // Shuffle the password to avoid predictable patterns\n  return password.split('').sort(() => Math.random() - 0.5).join('')\n}\n\n/**\n * Check if password needs to be changed based on age\n */\nexport function shouldChangePassword(passwordChangedAt: Date, maxAgeInDays: number = 90): boolean {\n  const now = new Date()\n  const ageInDays = (now.getTime() - passwordChangedAt.getTime()) / (1000 * 60 * 60 * 24)\n  return ageInDays > maxAgeInDays\n}\n\n/**\n * Generate password reset token\n */\nexport function generateResetToken(): string {\n  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'\n  let token = ''\n  for (let i = 0; i < 32; i++) {\n    token += chars[Math.floor(Math.random() * chars.length)]\n  }\n  return token\n}\n\n/**\n * Sanitize password for logging (replace with asterisks)\n */\nexport function sanitizePasswordForLog(password: string): string {\n  return '*'.repeat(Math.min(password.length, 8))\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AAGA,kCAAkC;AAClC,MAAM,cAAc;AACpB,MAAM,sBAAsB;AAC5B,MAAM,sBAAsB;AAE5B,mCAAmC;AACnC,MAAM,wBAAwB;IAC5B,WAAW;IACX,WAAW;IACX,kBAAkB;IAClB,kBAAkB;IAClB,gBAAgB;IAChB,qBAAqB;IACrB,iBAAiB;IACjB,wBAAwB;IACxB,2BAA2B;AAC7B;AAEA,kCAAkC;AAClC,MAAM,mBAAmB;IACvB;IAAY;IAAU;IAAa;IAAU;IAAU;IACvD;IAAS;IAAW;IAAW;IAAU;IAAc;IACvD;IAAY;IAAQ;IAAQ;IAAQ;IAAQ;IAAS;IAAQ;CAC9D;AAKM,eAAe,aAAa,QAAgB;IACjD,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG;QACtC,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI,SAAS,MAAM,GAAG,qBAAqB;QACzC,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,oBAAoB,WAAW,CAAC;IAC5E;IAEA,OAAO,MAAM,wIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AACrC;AAKO,eAAe,eAAe,QAAgB,EAAE,IAAY;IACjE,IAAI,CAAC,YAAY,CAAC,MAAM;QACtB,OAAO;IACT;IAEA,IAAI;QACF,OAAO,MAAM,wIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;IACxC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;AACF;AAKO,SAAS,iBACd,QAAgB,EAChB,QAKC;IAED,MAAM,SAAmB,EAAE;IAC3B,IAAI,QAAQ;IAEZ,eAAe;IACf,IAAI,CAAC,YAAY,SAAS,MAAM,GAAG,sBAAsB,SAAS,EAAE;QAClE,OAAO,IAAI,CAAC,CAAC,0BAA0B,EAAE,sBAAsB,SAAS,CAAC,gBAAgB,CAAC;IAC5F,OAAO;QACL,SAAS;IACX;IAEA,IAAI,SAAS,MAAM,GAAG,sBAAsB,SAAS,EAAE;QACrD,OAAO,IAAI,CAAC,CAAC,uBAAuB,EAAE,sBAAsB,SAAS,CAAC,WAAW,CAAC;IACpF;IAEA,8BAA8B;IAC9B,IAAI,sBAAsB,gBAAgB,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QACrE,OAAO,IAAI,CAAC;IACd,OAAO,IAAI,QAAQ,IAAI,CAAC,WAAW;QACjC,SAAS;IACX;IAEA,8BAA8B;IAC9B,IAAI,sBAAsB,gBAAgB,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QACrE,OAAO,IAAI,CAAC;IACd,OAAO,IAAI,QAAQ,IAAI,CAAC,WAAW;QACjC,SAAS;IACX;IAEA,oBAAoB;IACpB,IAAI,sBAAsB,cAAc,IAAI,CAAC,KAAK,IAAI,CAAC,WAAW;QAChE,OAAO,IAAI,CAAC;IACd,OAAO,IAAI,KAAK,IAAI,CAAC,WAAW;QAC9B,SAAS;IACX;IAEA,+BAA+B;IAC/B,MAAM,eAAe;IACrB,MAAM,mBAAmB,CAAC,SAAS,KAAK,CAAC,iBAAiB,EAAE,EAAE,MAAM;IAEpE,IAAI,sBAAsB,mBAAmB,IAAI,qBAAqB,GAAG;QACvE,OAAO,IAAI,CAAC;IACd,OAAO,IAAI,oBAAoB,sBAAsB,eAAe,EAAE;QACpE,SAAS;IACX;IAEA,iCAAiC;IACjC,IAAI,sBAAsB,sBAAsB,EAAE;QAChD,MAAM,gBAAgB,SAAS,WAAW;QAC1C,IAAI,iBAAiB,QAAQ,CAAC,gBAAgB;YAC5C,OAAO,IAAI,CAAC;QACd;IACF;IAEA,8CAA8C;IAC9C,IAAI,sBAAsB,yBAAyB,IAAI,UAAU;QAC/D,MAAM,gBAAgB,SAAS,WAAW;QAC1C,MAAM,aAAa;YACjB,SAAS,QAAQ,EAAE;YACnB,SAAS,KAAK,EAAE,cAAc,MAAM,IAAI,CAAC,EAAE;YAC3C,SAAS,SAAS,EAAE;YACpB,SAAS,QAAQ,EAAE;SACpB,CAAC,MAAM,CAAC;QAET,KAAK,MAAM,SAAS,WAAY;YAC9B,IAAI,SAAS,MAAM,MAAM,GAAG,KAAK,cAAc,QAAQ,CAAC,QAAQ;gBAC9D,OAAO,IAAI,CAAC;gBACZ;YACF;QACF;IACF;IAEA,+CAA+C;IAC/C,IAAI,SAAS,MAAM,IAAI,IAAI,SAAS;IACpC,IAAI,SAAS,MAAM,IAAI,IAAI,SAAS;IACpC,IAAI,oBAAoB,GAAG,SAAS;IACpC,IAAI,eAAe,IAAI,CAAC,WAAW,SAAS,GAAE,qBAAqB;IACnE,IAAI,SAAS,IAAI,CAAC,WAAW,SAAS,GAAE,mBAAmB;IAE3D,qBAAqB;IACrB,IAAI;IACJ,IAAI,SAAS,GAAG;QACd,WAAW;IACb,OAAO,IAAI,SAAS,GAAG;QACrB,WAAW;IACb,OAAO;QACL,WAAW;IACb;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;QACA;QACA,OAAO,KAAK,GAAG,CAAC,OAAO,IAAI,YAAY;IACzC;AACF;AAKO,SAAS,0BAA0B,SAAiB,EAAE;IAC3D,MAAM,YAAY;IAClB,MAAM,YAAY;IAClB,MAAM,UAAU;IAChB,MAAM,eAAe;IAErB,MAAM,WAAW,YAAY,YAAY,UAAU;IAEnD,IAAI,WAAW;IAEf,4DAA4D;IAC5D,YAAY,SAAS,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,UAAU,MAAM,EAAE;IACnE,YAAY,SAAS,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,UAAU,MAAM,EAAE;IACnE,YAAY,OAAO,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,MAAM,EAAE;IAC/D,YAAY,YAAY,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,aAAa,MAAM,EAAE;IAEzE,yBAAyB;IACzB,IAAK,IAAI,IAAI,SAAS,MAAM,EAAE,IAAI,QAAQ,IAAK;QAC7C,YAAY,QAAQ,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS,MAAM,EAAE;IACnE;IAEA,qDAAqD;IACrD,OAAO,SAAS,KAAK,CAAC,IAAI,IAAI,CAAC,IAAM,KAAK,MAAM,KAAK,KAAK,IAAI,CAAC;AACjE;AAKO,SAAS,qBAAqB,iBAAuB,EAAE,eAAuB,EAAE;IACrF,MAAM,MAAM,IAAI;IAChB,MAAM,YAAY,CAAC,IAAI,OAAO,KAAK,kBAAkB,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;IACtF,OAAO,YAAY;AACrB;AAKO,SAAS;IACd,MAAM,QAAQ;IACd,IAAI,QAAQ;IACZ,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;QAC3B,SAAS,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM,EAAE;IAC1D;IACA,OAAO;AACT;AAKO,SAAS,uBAAuB,QAAgB;IACrD,OAAO,IAAI,MAAM,CAAC,KAAK,GAAG,CAAC,SAAS,MAAM,EAAE;AAC9C"}}, {"offset": {"line": 1850, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/lib/auth/security.ts"], "sourcesContent": ["import { prisma } from '@/lib/prisma'\nimport { SecurityEventType, SecuritySeverity, UserRole } from '@/lib/generated/prisma'\nimport { SecurityEvent, RateLimitInfo, AuditLogEntry } from './types'\n\n// Rate limiting configuration\nconst RATE_LIMITS = {\n  login: {\n    windowMs: 15 * 60 * 1000, // 15 minutes\n    maxAttempts: 5,\n    blockDurationMs: 30 * 60 * 1000 // 30 minutes\n  },\n  api: {\n    windowMs: 60 * 1000, // 1 minute\n    maxRequests: 100\n  },\n  passwordReset: {\n    windowMs: 60 * 60 * 1000, // 1 hour\n    maxAttempts: 3\n  }\n}\n\n// Account lockout configuration\nconst LOCKOUT_CONFIG = {\n  maxFailedAttempts: 5,\n  lockoutDurationMs: 30 * 60 * 1000, // 30 minutes\n  progressiveLockout: true // Increase lockout time with repeated failures\n}\n\n/**\n * Check if user account should be locked due to failed login attempts\n */\nexport async function checkAccountLockout(userId: string): Promise<{\n  isLocked: boolean\n  lockoutUntil?: Date\n  failedAttempts: number\n}> {\n  const user = await prisma.user.findUnique({\n    where: { id: userId },\n    select: {\n      failedLoginAttempts: true,\n      lockedUntil: true\n    }\n  })\n\n  if (!user) {\n    return { isLocked: false, failedAttempts: 0 }\n  }\n\n  const now = new Date()\n  const isCurrentlyLocked = user.lockedUntil && user.lockedUntil > now\n\n  return {\n    isLocked: !!isCurrentlyLocked,\n    lockoutUntil: user.lockedUntil || undefined,\n    failedAttempts: user.failedLoginAttempts\n  }\n}\n\n/**\n * Handle failed login attempt\n */\nexport async function handleFailedLogin(\n  userId: string,\n  ipAddress: string,\n  userAgent?: string\n): Promise<{ shouldLock: boolean; lockoutUntil?: Date }> {\n  const user = await prisma.user.findUnique({\n    where: { id: userId },\n    select: {\n      failedLoginAttempts: true,\n      lockedUntil: true\n    }\n  })\n\n  if (!user) {\n    return { shouldLock: false }\n  }\n\n  const newFailedAttempts = user.failedLoginAttempts + 1\n  const shouldLock = newFailedAttempts >= LOCKOUT_CONFIG.maxFailedAttempts\n\n  let lockoutUntil: Date | undefined\n  if (shouldLock) {\n    const lockoutDuration = LOCKOUT_CONFIG.progressiveLockout\n      ? LOCKOUT_CONFIG.lockoutDurationMs * Math.pow(2, Math.floor(newFailedAttempts / LOCKOUT_CONFIG.maxFailedAttempts) - 1)\n      : LOCKOUT_CONFIG.lockoutDurationMs\n\n    lockoutUntil = new Date(Date.now() + lockoutDuration)\n  }\n\n  // Update user record\n  await prisma.user.update({\n    where: { id: userId },\n    data: {\n      failedLoginAttempts: newFailedAttempts,\n      lockedUntil: lockoutUntil\n    }\n  })\n\n  // Log security event\n  await logSecurityEvent({\n    userId,\n    eventType: shouldLock ? SecurityEventType.ACCOUNT_LOCKED : SecurityEventType.LOGIN_FAILURE,\n    severity: shouldLock ? SecuritySeverity.HIGH : SecuritySeverity.MEDIUM,\n    description: shouldLock \n      ? `Account locked after ${newFailedAttempts} failed login attempts`\n      : `Failed login attempt (${newFailedAttempts}/${LOCKOUT_CONFIG.maxFailedAttempts})`,\n    ipAddress,\n    userAgent\n  })\n\n  return { shouldLock, lockoutUntil }\n}\n\n/**\n * Handle successful login\n */\nexport async function handleSuccessfulLogin(\n  userId: string,\n  ipAddress: string,\n  userAgent?: string\n): Promise<void> {\n  // Reset failed login attempts\n  await prisma.user.update({\n    where: { id: userId },\n    data: {\n      failedLoginAttempts: 0,\n      lockedUntil: null,\n      lastLoginAt: new Date(),\n      lastLoginIp: ipAddress\n    }\n  })\n\n  // Log successful login\n  await logSecurityEvent({\n    userId,\n    eventType: SecurityEventType.LOGIN_SUCCESS,\n    severity: SecuritySeverity.LOW,\n    description: 'Successful login',\n    ipAddress,\n    userAgent\n  })\n}\n\n/**\n * Log security event\n */\nexport async function logSecurityEvent(event: {\n  userId?: string\n  eventType: SecurityEventType\n  severity: SecuritySeverity\n  description: string\n  ipAddress?: string\n  userAgent?: string\n  additionalData?: Record<string, any>\n}): Promise<void> {\n  try {\n    await prisma.securityEvent.create({\n      data: {\n        userId: event.userId,\n        eventType: event.eventType,\n        severity: event.severity,\n        description: event.description,\n        ipAddress: event.ipAddress,\n        userAgent: event.userAgent,\n        additionalData: event.additionalData ? JSON.stringify(event.additionalData) : null\n      }\n    })\n  } catch (error) {\n    console.error('Failed to log security event:', error)\n  }\n}\n\n/**\n * Log audit trail entry\n */\nexport async function logAuditEntry(entry: {\n  userId?: string\n  action: string\n  entityType: string\n  entityId?: string\n  oldValues?: Record<string, any>\n  newValues?: Record<string, any>\n  ipAddress?: string\n  userAgent?: string\n}): Promise<void> {\n  try {\n    await prisma.auditLog.create({\n      data: {\n        userId: entry.userId,\n        action: entry.action,\n        entityType: entry.entityType,\n        entityId: entry.entityId,\n        oldValues: entry.oldValues ? JSON.stringify(entry.oldValues) : null,\n        newValues: entry.newValues ? JSON.stringify(entry.newValues) : null,\n        ipAddress: entry.ipAddress,\n        userAgent: entry.userAgent\n      }\n    })\n  } catch (error) {\n    console.error('Failed to log audit entry:', error)\n  }\n}\n\n/**\n * Check rate limit for IP address\n */\nexport async function checkRateLimit(\n  key: string,\n  limit: number,\n  windowMs: number\n): Promise<RateLimitInfo> {\n  // In a production environment, you would use Redis or similar\n  // For now, we'll use a simple in-memory store\n  const now = Date.now()\n  const windowStart = now - windowMs\n\n  // This is a simplified implementation\n  // In production, use a proper rate limiting solution like Redis\n  const rateLimitKey = `rate_limit:${key}`\n  \n  // For demonstration, we'll return a basic rate limit info\n  // In production, implement proper rate limiting with Redis\n  return {\n    limit,\n    remaining: limit - 1,\n    resetTime: new Date(now + windowMs),\n    blocked: false\n  }\n}\n\n/**\n * Validate user permissions for an action\n */\nexport function hasPermission(\n  userRole: UserRole,\n  userPermissions: string[] | undefined,\n  requiredPermission: string\n): boolean {\n  // Admin has all permissions\n  if (userRole === UserRole.ADMIN) {\n    return true\n  }\n\n  // Principal has most permissions except system administration\n  if (userRole === UserRole.PRINCIPAL) {\n    const restrictedPermissions = ['system.admin', 'user.create', 'user.delete']\n    return !restrictedPermissions.includes(requiredPermission)\n  }\n\n  // Check specific permissions\n  if (userPermissions && userPermissions.includes(requiredPermission)) {\n    return true\n  }\n\n  // Role-based permissions\n  const rolePermissions: Record<UserRole, string[]> = {\n    [UserRole.ADMIN]: ['*'], // All permissions\n    [UserRole.PRINCIPAL]: [\n      'students.read', 'students.write', 'students.delete',\n      'attendance.read', 'attendance.write',\n      'reports.read', 'reports.generate',\n      'analytics.read', 'analytics.export',\n      'teachers.read', 'teachers.write'\n    ],\n    [UserRole.TEACHER]: [\n      'students.read',\n      'attendance.read', 'attendance.write',\n      'reports.read',\n      'scanner.use'\n    ],\n    [UserRole.SCANNER]: [\n      'scanner.use',\n      'attendance.write',\n      'students.read.basic'\n    ]\n  }\n\n  const permissions = rolePermissions[userRole] || []\n  return permissions.includes('*') || permissions.includes(requiredPermission)\n}\n\n/**\n * Get client IP address from request\n */\nexport function getClientIP(request: Request): string {\n  const forwarded = request.headers.get('x-forwarded-for')\n  const realIP = request.headers.get('x-real-ip')\n  const remoteAddr = request.headers.get('remote-addr')\n  \n  if (forwarded) {\n    return forwarded.split(',')[0].trim()\n  }\n  \n  return realIP || remoteAddr || 'unknown'\n}\n\n/**\n * Get user agent from request\n */\nexport function getUserAgent(request: Request): string {\n  return request.headers.get('user-agent') || 'unknown'\n}\n\n/**\n * Sanitize sensitive data for logging\n */\nexport function sanitizeForLog(data: any): any {\n  if (typeof data !== 'object' || data === null) {\n    return data\n  }\n\n  const sensitiveFields = ['password', 'passwordHash', 'token', 'secret', 'key']\n  const sanitized = { ...data }\n\n  for (const field of sensitiveFields) {\n    if (field in sanitized) {\n      sanitized[field] = '[REDACTED]'\n    }\n  }\n\n  return sanitized\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAGA,8BAA8B;AAC9B,MAAM,cAAc;IAClB,OAAO;QACL,UAAU,KAAK,KAAK;QACpB,aAAa;QACb,iBAAiB,KAAK,KAAK,KAAK,aAAa;IAC/C;IACA,KAAK;QACH,UAAU,KAAK;QACf,aAAa;IACf;IACA,eAAe;QACb,UAAU,KAAK,KAAK;QACpB,aAAa;IACf;AACF;AAEA,gCAAgC;AAChC,MAAM,iBAAiB;IACrB,mBAAmB;IACnB,mBAAmB,KAAK,KAAK;IAC7B,oBAAoB,KAAK,+CAA+C;AAC1E;AAKO,eAAe,oBAAoB,MAAc;IAKtD,MAAM,OAAO,MAAM,qHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QACxC,OAAO;YAAE,IAAI;QAAO;QACpB,QAAQ;YACN,qBAAqB;YACrB,aAAa;QACf;IACF;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;YAAE,UAAU;YAAO,gBAAgB;QAAE;IAC9C;IAEA,MAAM,MAAM,IAAI;IAChB,MAAM,oBAAoB,KAAK,WAAW,IAAI,KAAK,WAAW,GAAG;IAEjE,OAAO;QACL,UAAU,CAAC,CAAC;QACZ,cAAc,KAAK,WAAW,IAAI;QAClC,gBAAgB,KAAK,mBAAmB;IAC1C;AACF;AAKO,eAAe,kBACpB,MAAc,EACd,SAAiB,EACjB,SAAkB;IAElB,MAAM,OAAO,MAAM,qHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QACxC,OAAO;YAAE,IAAI;QAAO;QACpB,QAAQ;YACN,qBAAqB;YACrB,aAAa;QACf;IACF;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;YAAE,YAAY;QAAM;IAC7B;IAEA,MAAM,oBAAoB,KAAK,mBAAmB,GAAG;IACrD,MAAM,aAAa,qBAAqB,eAAe,iBAAiB;IAExE,IAAI;IACJ,IAAI,YAAY;QACd,MAAM,kBAAkB,eAAe,kBAAkB,GACrD,eAAe,iBAAiB,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,oBAAoB,eAAe,iBAAiB,IAAI,KAClH,eAAe,iBAAiB;QAEpC,eAAe,IAAI,KAAK,KAAK,GAAG,KAAK;IACvC;IAEA,qBAAqB;IACrB,MAAM,qHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;QACvB,OAAO;YAAE,IAAI;QAAO;QACpB,MAAM;YACJ,qBAAqB;YACrB,aAAa;QACf;IACF;IAEA,qBAAqB;IACrB,MAAM,iBAAiB;QACrB;QACA,WAAW,aAAa,sJAAA,CAAA,oBAAiB,CAAC,cAAc,GAAG,sJAAA,CAAA,oBAAiB,CAAC,aAAa;QAC1F,UAAU,aAAa,sJAAA,CAAA,mBAAgB,CAAC,IAAI,GAAG,sJAAA,CAAA,mBAAgB,CAAC,MAAM;QACtE,aAAa,aACT,CAAC,qBAAqB,EAAE,kBAAkB,sBAAsB,CAAC,GACjE,CAAC,sBAAsB,EAAE,kBAAkB,CAAC,EAAE,eAAe,iBAAiB,CAAC,CAAC,CAAC;QACrF;QACA;IACF;IAEA,OAAO;QAAE;QAAY;IAAa;AACpC;AAKO,eAAe,sBACpB,MAAc,EACd,SAAiB,EACjB,SAAkB;IAElB,8BAA8B;IAC9B,MAAM,qHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;QACvB,OAAO;YAAE,IAAI;QAAO;QACpB,MAAM;YACJ,qBAAqB;YACrB,aAAa;YACb,aAAa,IAAI;YACjB,aAAa;QACf;IACF;IAEA,uBAAuB;IACvB,MAAM,iBAAiB;QACrB;QACA,WAAW,sJAAA,CAAA,oBAAiB,CAAC,aAAa;QAC1C,UAAU,sJAAA,CAAA,mBAAgB,CAAC,GAAG;QAC9B,aAAa;QACb;QACA;IACF;AACF;AAKO,eAAe,iBAAiB,KAQtC;IACC,IAAI;QACF,MAAM,qHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,MAAM,CAAC;YAChC,MAAM;gBACJ,QAAQ,MAAM,MAAM;gBACpB,WAAW,MAAM,SAAS;gBAC1B,UAAU,MAAM,QAAQ;gBACxB,aAAa,MAAM,WAAW;gBAC9B,WAAW,MAAM,SAAS;gBAC1B,WAAW,MAAM,SAAS;gBAC1B,gBAAgB,MAAM,cAAc,GAAG,KAAK,SAAS,CAAC,MAAM,cAAc,IAAI;YAChF;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;IACjD;AACF;AAKO,eAAe,cAAc,KASnC;IACC,IAAI;QACF,MAAM,qHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC3B,MAAM;gBACJ,QAAQ,MAAM,MAAM;gBACpB,QAAQ,MAAM,MAAM;gBACpB,YAAY,MAAM,UAAU;gBAC5B,UAAU,MAAM,QAAQ;gBACxB,WAAW,MAAM,SAAS,GAAG,KAAK,SAAS,CAAC,MAAM,SAAS,IAAI;gBAC/D,WAAW,MAAM,SAAS,GAAG,KAAK,SAAS,CAAC,MAAM,SAAS,IAAI;gBAC/D,WAAW,MAAM,SAAS;gBAC1B,WAAW,MAAM,SAAS;YAC5B;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;IAC9C;AACF;AAKO,eAAe,eACpB,GAAW,EACX,KAAa,EACb,QAAgB;IAEhB,8DAA8D;IAC9D,8CAA8C;IAC9C,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,cAAc,MAAM;IAE1B,sCAAsC;IACtC,gEAAgE;IAChE,MAAM,eAAe,CAAC,WAAW,EAAE,KAAK;IAExC,0DAA0D;IAC1D,2DAA2D;IAC3D,OAAO;QACL;QACA,WAAW,QAAQ;QACnB,WAAW,IAAI,KAAK,MAAM;QAC1B,SAAS;IACX;AACF;AAKO,SAAS,cACd,QAAkB,EAClB,eAAqC,EACrC,kBAA0B;IAE1B,4BAA4B;IAC5B,IAAI,aAAa,sJAAA,CAAA,WAAQ,CAAC,KAAK,EAAE;QAC/B,OAAO;IACT;IAEA,8DAA8D;IAC9D,IAAI,aAAa,sJAAA,CAAA,WAAQ,CAAC,SAAS,EAAE;QACnC,MAAM,wBAAwB;YAAC;YAAgB;YAAe;SAAc;QAC5E,OAAO,CAAC,sBAAsB,QAAQ,CAAC;IACzC;IAEA,6BAA6B;IAC7B,IAAI,mBAAmB,gBAAgB,QAAQ,CAAC,qBAAqB;QACnE,OAAO;IACT;IAEA,yBAAyB;IACzB,MAAM,kBAA8C;QAClD,CAAC,sJAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,EAAE;YAAC;SAAI;QACvB,CAAC,sJAAA,CAAA,WAAQ,CAAC,SAAS,CAAC,EAAE;YACpB;YAAiB;YAAkB;YACnC;YAAmB;YACnB;YAAgB;YAChB;YAAkB;YAClB;YAAiB;SAClB;QACD,CAAC,sJAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,EAAE;YAClB;YACA;YAAmB;YACnB;YACA;SACD;QACD,CAAC,sJAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,EAAE;YAClB;YACA;YACA;SACD;IACH;IAEA,MAAM,cAAc,eAAe,CAAC,SAAS,IAAI,EAAE;IACnD,OAAO,YAAY,QAAQ,CAAC,QAAQ,YAAY,QAAQ,CAAC;AAC3D;AAKO,SAAS,YAAY,OAAgB;IAC1C,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC;IACtC,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;IACnC,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IAEvC,IAAI,WAAW;QACb,OAAO,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;IACrC;IAEA,OAAO,UAAU,cAAc;AACjC;AAKO,SAAS,aAAa,OAAgB;IAC3C,OAAO,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB;AAC9C;AAKO,SAAS,eAAe,IAAS;IACtC,IAAI,OAAO,SAAS,YAAY,SAAS,MAAM;QAC7C,OAAO;IACT;IAEA,MAAM,kBAAkB;QAAC;QAAY;QAAgB;QAAS;QAAU;KAAM;IAC9E,MAAM,YAAY;QAAE,GAAG,IAAI;IAAC;IAE5B,KAAK,MAAM,SAAS,gBAAiB;QACnC,IAAI,SAAS,WAAW;YACtB,SAAS,CAAC,MAAM,GAAG;QACrB;IACF;IAEA,OAAO;AACT"}}, {"offset": {"line": 2122, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/lib/auth/config.ts"], "sourcesContent": ["import NextAuth from \"next-auth\"\nimport Credentials from \"next-auth/providers/credentials\"\nimport { prisma } from \"@/lib/prisma\"\nimport { verifyPassword } from \"./password\"\nimport { handleSuccessfulLogin, handleFailedLogin, checkAccountLockout, logSecurityEvent } from \"./security\"\nimport { SecurityEventType, SecuritySeverity, UserRole } from \"@/lib/generated/prisma\"\nimport { AuthUser, LoginCredentials } from \"./types\"\n\n/**\n * Authenticate user with username/email and password\n */\nasync function authenticateUser(credentials: LoginCredentials, request?: Request): Promise<AuthUser | null> {\n  try {\n    const { username, password } = credentials\n\n    if (!username || !password) {\n      return null\n    }\n\n    // Find user by username or email\n    const user = await prisma.user.findFirst({\n      where: {\n        OR: [\n          { username: username },\n          { email: username }\n        ]\n      },\n      include: {\n        teacher: true\n      }\n    })\n\n    if (!user) {\n      // Log failed login attempt for unknown user\n      if (request) {\n        await logSecurityEvent({\n          eventType: SecurityEventType.LOGIN_FAILURE,\n          severity: SecuritySeverity.MEDIUM,\n          description: `Login attempt with unknown username: ${username}`,\n          ipAddress: getClientIP(request),\n          userAgent: getUserAgent(request)\n        })\n      }\n      return null\n    }\n\n    // Check if account is active\n    if (!user.isActive) {\n      if (request) {\n        await logSecurityEvent({\n          userId: user.id,\n          eventType: SecurityEventType.LOGIN_FAILURE,\n          severity: SecuritySeverity.HIGH,\n          description: 'Login attempt on inactive account',\n          ipAddress: getClientIP(request),\n          userAgent: getUserAgent(request)\n        })\n      }\n      return null\n    }\n\n    // Check account lockout\n    const lockoutStatus = await checkAccountLockout(user.id)\n    if (lockoutStatus.isLocked) {\n      if (request) {\n        await logSecurityEvent({\n          userId: user.id,\n          eventType: SecurityEventType.LOGIN_FAILURE,\n          severity: SecuritySeverity.HIGH,\n          description: 'Login attempt on locked account',\n          ipAddress: getClientIP(request),\n          userAgent: getUserAgent(request)\n        })\n      }\n      return null\n    }\n\n    // Verify password\n    const isValidPassword = await verifyPassword(password, user.passwordHash)\n\n    if (!isValidPassword) {\n      // Handle failed login\n      if (request) {\n        await handleFailedLogin(\n          user.id,\n          getClientIP(request),\n          getUserAgent(request)\n        )\n      }\n      return null\n    }\n\n    // Handle successful login\n    if (request) {\n      await handleSuccessfulLogin(\n        user.id,\n        getClientIP(request),\n        getUserAgent(request)\n      )\n    }\n\n    // Parse permissions\n    let permissions: string[] = []\n    if (user.permissions) {\n      try {\n        permissions = JSON.parse(user.permissions)\n      } catch (error) {\n        console.error('Failed to parse user permissions:', error)\n      }\n    }\n\n    return {\n      id: user.id,\n      username: user.username,\n      email: user.email,\n      firstName: user.firstName,\n      lastName: user.lastName,\n      middleName: user.middleName,\n      role: user.role,\n      permissions,\n      isActive: user.isActive,\n      lastLoginAt: user.lastLoginAt,\n      mustChangePassword: user.mustChangePassword,\n      twoFactorEnabled: user.twoFactorEnabled,\n      phoneNumber: user.phoneNumber,\n      department: user.department,\n      position: user.position\n    }\n  } catch (error) {\n    console.error('Authentication error:', error)\n    return null\n  }\n}\n\n// Helper functions for request handling\nfunction getClientIP(request: Request): string {\n  const forwarded = request.headers.get('x-forwarded-for')\n  const realIP = request.headers.get('x-real-ip')\n\n  if (forwarded) {\n    return forwarded.split(',')[0].trim()\n  }\n\n  return realIP || 'unknown'\n}\n\nfunction getUserAgent(request: Request): string {\n  return request.headers.get('user-agent') || 'unknown'\n}\n\nexport const { handlers, signIn, signOut, auth } = NextAuth({\n  providers: [\n    Credentials({\n      name: \"credentials\",\n      credentials: {\n        username: {\n          label: \"Username or Email\",\n          type: \"text\",\n          placeholder: \"Enter your username or email\"\n        },\n        password: {\n          label: \"Password\",\n          type: \"password\",\n          placeholder: \"Enter your password\"\n        }\n      },\n      async authorize(credentials, request) {\n        if (!credentials?.username || !credentials?.password) {\n          return null\n        }\n\n        const user = await authenticateUser({\n          username: credentials.username as string,\n          password: credentials.password as string\n        }, request as Request)\n\n        if (user) {\n          return {\n            id: user.id,\n            email: user.email,\n            name: `${user.firstName} ${user.lastName}`,\n            role: user.role,\n            permissions: user.permissions,\n            isActive: user.isActive,\n            mustChangePassword: user.mustChangePassword\n          }\n        }\n\n        return null\n      }\n    })\n  ],\n  pages: {\n    signIn: \"/login\",\n    error: \"/login\"\n  },\n  callbacks: {\n    async jwt({ token, user, trigger, session }) {\n      // Initial sign in\n      if (user) {\n        token.role = user.role\n        token.permissions = user.permissions\n        token.isActive = user.isActive\n        token.mustChangePassword = user.mustChangePassword\n      }\n\n      // Handle session updates\n      if (trigger === \"update\" && session) {\n        token.role = session.role || token.role\n        token.permissions = session.permissions || token.permissions\n        token.isActive = session.isActive ?? token.isActive\n        token.mustChangePassword = session.mustChangePassword ?? token.mustChangePassword\n      }\n\n      return token\n    },\n    async session({ session, token }) {\n      if (token && session.user) {\n        session.user.id = token.sub!\n        session.user.role = token.role as UserRole\n        session.user.permissions = token.permissions as string[]\n        session.user.isActive = token.isActive as boolean\n        session.user.mustChangePassword = token.mustChangePassword as boolean\n      }\n      return session\n    },\n    async signIn({ user, account, profile, email, credentials }) {\n      // Additional sign-in validation can be added here\n      return true\n    }\n  },\n  session: {\n    strategy: \"jwt\",\n    maxAge: 8 * 60 * 60, // 8 hours\n    updateAge: 60 * 60, // 1 hour\n  },\n  jwt: {\n    maxAge: 8 * 60 * 60, // 8 hours\n  },\n  events: {\n    async signIn({ user, account, profile, isNewUser }) {\n      // Log successful sign in\n      console.log(`User ${user.email} signed in successfully`)\n    },\n    async signOut({ token, session }) {\n      // Log sign out\n      if (token?.sub) {\n        await logSecurityEvent({\n          userId: token.sub,\n          eventType: SecurityEventType.LOGOUT,\n          severity: SecuritySeverity.LOW,\n          description: 'User logged out'\n        })\n      }\n    }\n  }\n})\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAGA;;CAEC,GACD,eAAe,iBAAiB,WAA6B,EAAE,OAAiB;IAC9E,IAAI;QACF,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG;QAE/B,IAAI,CAAC,YAAY,CAAC,UAAU;YAC1B,OAAO;QACT;QAEA,iCAAiC;QACjC,MAAM,OAAO,MAAM,qHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,SAAS,CAAC;YACvC,OAAO;gBACL,IAAI;oBACF;wBAAE,UAAU;oBAAS;oBACrB;wBAAE,OAAO;oBAAS;iBACnB;YACH;YACA,SAAS;gBACP,SAAS;YACX;QACF;QAEA,IAAI,CAAC,MAAM;YACT,4CAA4C;YAC5C,IAAI,SAAS;gBACX,MAAM,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE;oBACrB,WAAW,sJAAA,CAAA,oBAAiB,CAAC,aAAa;oBAC1C,UAAU,sJAAA,CAAA,mBAAgB,CAAC,MAAM;oBACjC,aAAa,CAAC,qCAAqC,EAAE,UAAU;oBAC/D,WAAW,YAAY;oBACvB,WAAW,aAAa;gBAC1B;YACF;YACA,OAAO;QACT;QAEA,6BAA6B;QAC7B,IAAI,CAAC,KAAK,QAAQ,EAAE;YAClB,IAAI,SAAS;gBACX,MAAM,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE;oBACrB,QAAQ,KAAK,EAAE;oBACf,WAAW,sJAAA,CAAA,oBAAiB,CAAC,aAAa;oBAC1C,UAAU,sJAAA,CAAA,mBAAgB,CAAC,IAAI;oBAC/B,aAAa;oBACb,WAAW,YAAY;oBACvB,WAAW,aAAa;gBAC1B;YACF;YACA,OAAO;QACT;QAEA,wBAAwB;QACxB,MAAM,gBAAgB,MAAM,CAAA,GAAA,+HAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,EAAE;QACvD,IAAI,cAAc,QAAQ,EAAE;YAC1B,IAAI,SAAS;gBACX,MAAM,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE;oBACrB,QAAQ,KAAK,EAAE;oBACf,WAAW,sJAAA,CAAA,oBAAiB,CAAC,aAAa;oBAC1C,UAAU,sJAAA,CAAA,mBAAgB,CAAC,IAAI;oBAC/B,aAAa;oBACb,WAAW,YAAY;oBACvB,WAAW,aAAa;gBAC1B;YACF;YACA,OAAO;QACT;QAEA,kBAAkB;QAClB,MAAM,kBAAkB,MAAM,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,KAAK,YAAY;QAExE,IAAI,CAAC,iBAAiB;YACpB,sBAAsB;YACtB,IAAI,SAAS;gBACX,MAAM,CAAA,GAAA,+HAAA,CAAA,oBAAiB,AAAD,EACpB,KAAK,EAAE,EACP,YAAY,UACZ,aAAa;YAEjB;YACA,OAAO;QACT;QAEA,0BAA0B;QAC1B,IAAI,SAAS;YACX,MAAM,CAAA,GAAA,+HAAA,CAAA,wBAAqB,AAAD,EACxB,KAAK,EAAE,EACP,YAAY,UACZ,aAAa;QAEjB;QAEA,oBAAoB;QACpB,IAAI,cAAwB,EAAE;QAC9B,IAAI,KAAK,WAAW,EAAE;YACpB,IAAI;gBACF,cAAc,KAAK,KAAK,CAAC,KAAK,WAAW;YAC3C,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qCAAqC;YACrD;QACF;QAEA,OAAO;YACL,IAAI,KAAK,EAAE;YACX,UAAU,KAAK,QAAQ;YACvB,OAAO,KAAK,KAAK;YACjB,WAAW,KAAK,SAAS;YACzB,UAAU,KAAK,QAAQ;YACvB,YAAY,KAAK,UAAU;YAC3B,MAAM,KAAK,IAAI;YACf;YACA,UAAU,KAAK,QAAQ;YACvB,aAAa,KAAK,WAAW;YAC7B,oBAAoB,KAAK,kBAAkB;YAC3C,kBAAkB,KAAK,gBAAgB;YACvC,aAAa,KAAK,WAAW;YAC7B,YAAY,KAAK,UAAU;YAC3B,UAAU,KAAK,QAAQ;QACzB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO;IACT;AACF;AAEA,wCAAwC;AACxC,SAAS,YAAY,OAAgB;IACnC,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC;IACtC,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;IAEnC,IAAI,WAAW;QACb,OAAO,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;IACrC;IAEA,OAAO,UAAU;AACnB;AAEA,SAAS,aAAa,OAAgB;IACpC,OAAO,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB;AAC9C;AAEO,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,6JAAA,CAAA,UAAQ,AAAD,EAAE;IAC1D,WAAW;QACT,CAAA,GAAA,kKAAA,CAAA,UAAW,AAAD,EAAE;YACV,MAAM;YACN,aAAa;gBACX,UAAU;oBACR,OAAO;oBACP,MAAM;oBACN,aAAa;gBACf;gBACA,UAAU;oBACR,OAAO;oBACP,MAAM;oBACN,aAAa;gBACf;YACF;YACA,MAAM,WAAU,WAAW,EAAE,OAAO;gBAClC,IAAI,CAAC,aAAa,YAAY,CAAC,aAAa,UAAU;oBACpD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,iBAAiB;oBAClC,UAAU,YAAY,QAAQ;oBAC9B,UAAU,YAAY,QAAQ;gBAChC,GAAG;gBAEH,IAAI,MAAM;oBACR,OAAO;wBACL,IAAI,KAAK,EAAE;wBACX,OAAO,KAAK,KAAK;wBACjB,MAAM,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;wBAC1C,MAAM,KAAK,IAAI;wBACf,aAAa,KAAK,WAAW;wBAC7B,UAAU,KAAK,QAAQ;wBACvB,oBAAoB,KAAK,kBAAkB;oBAC7C;gBACF;gBAEA,OAAO;YACT;QACF;KACD;IACD,OAAO;QACL,QAAQ;QACR,OAAO;IACT;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;YACzC,kBAAkB;YAClB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,WAAW,GAAG,KAAK,WAAW;gBACpC,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,kBAAkB,GAAG,KAAK,kBAAkB;YACpD;YAEA,yBAAyB;YACzB,IAAI,YAAY,YAAY,SAAS;gBACnC,MAAM,IAAI,GAAG,QAAQ,IAAI,IAAI,MAAM,IAAI;gBACvC,MAAM,WAAW,GAAG,QAAQ,WAAW,IAAI,MAAM,WAAW;gBAC5D,MAAM,QAAQ,GAAG,QAAQ,QAAQ,IAAI,MAAM,QAAQ;gBACnD,MAAM,kBAAkB,GAAG,QAAQ,kBAAkB,IAAI,MAAM,kBAAkB;YACnF;YAEA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,SAAS,QAAQ,IAAI,EAAE;gBACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,WAAW,GAAG,MAAM,WAAW;gBAC5C,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,kBAAkB,GAAG,MAAM,kBAAkB;YAC5D;YACA,OAAO;QACT;QACA,MAAM,QAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE;YACzD,kDAAkD;YAClD,OAAO;QACT;IACF;IACA,SAAS;QACP,UAAU;QACV,QAAQ,IAAI,KAAK;QACjB,WAAW,KAAK;IAClB;IACA,KAAK;QACH,QAAQ,IAAI,KAAK;IACnB;IACA,QAAQ;QACN,MAAM,QAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE;YAChD,yBAAyB;YACzB,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,uBAAuB,CAAC;QACzD;QACA,MAAM,SAAQ,EAAE,KAAK,EAAE,OAAO,EAAE;YAC9B,eAAe;YACf,IAAI,OAAO,KAAK;gBACd,MAAM,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE;oBACrB,QAAQ,MAAM,GAAG;oBACjB,WAAW,sJAAA,CAAA,oBAAiB,CAAC,MAAM;oBACnC,UAAU,sJAAA,CAAA,mBAAgB,CAAC,GAAG;oBAC9B,aAAa;gBACf;YACF;QACF;IACF;AACF"}}, {"offset": {"line": 2370, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["import { auth } from \"@/lib/auth/config\"\nimport { NextResponse } from \"next/server\"\nimport { UserRole } from \"@/lib/generated/prisma\"\n\n// Route permissions mapping\nconst ROUTE_PERMISSIONS: Record<string, { roles: UserRole[], permissions?: string[] }> = {\n  // Dashboard routes\n  '/dashboard': { roles: [UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.TEACHER, UserRole.SCANNER] },\n\n  // Student management\n  '/students': { roles: [UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.TEACHER] },\n  '/students/create': { roles: [UserRole.ADMIN, UserRole.PRINCIPAL] },\n  '/students/edit': { roles: [UserRole.ADMIN, UserRole.PRINCIPAL] },\n\n  // Attendance\n  '/attendance': { roles: [UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.TEACHER] },\n  '/scanner': { roles: [UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.TEACHER, UserRole.SCANNER] },\n\n  // Reports\n  '/reports': { roles: [UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.TEACHER] },\n  '/analytics': { roles: [UserRole.ADMIN, UserRole.PRINCIPAL] },\n\n  // User management\n  '/users': { roles: [UserRole.ADMIN] },\n  '/settings': { roles: [UserRole.ADMIN, UserRole.PRINCIPAL] }\n}\n\n// API route permissions\nconst API_PERMISSIONS: Record<string, { roles: UserRole[], permissions?: string[] }> = {\n  // Authentication APIs\n  '/api/auth/users': { roles: [UserRole.ADMIN] },\n  '/api/auth/profile': { roles: [UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.TEACHER, UserRole.SCANNER] },\n\n  // Student APIs\n  '/api/students': { roles: [UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.TEACHER] },\n\n  // Attendance APIs\n  '/api/attendance': { roles: [UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.TEACHER] },\n  '/api/scanner': { roles: [UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.TEACHER, UserRole.SCANNER] },\n\n  // Analytics APIs\n  '/api/analytics': { roles: [UserRole.ADMIN, UserRole.PRINCIPAL] },\n\n  // Reports APIs\n  '/api/reports': { roles: [UserRole.ADMIN, UserRole.PRINCIPAL, UserRole.TEACHER] }\n}\n\nfunction hasRouteAccess(pathname: string, userRole: UserRole, userPermissions?: string[]): boolean {\n  // Check exact route match first\n  const exactMatch = ROUTE_PERMISSIONS[pathname] || API_PERMISSIONS[pathname]\n  if (exactMatch) {\n    return exactMatch.roles.includes(userRole)\n  }\n\n  // Check pattern matches\n  for (const [route, config] of Object.entries({ ...ROUTE_PERMISSIONS, ...API_PERMISSIONS })) {\n    if (pathname.startsWith(route)) {\n      return config.roles.includes(userRole)\n    }\n  }\n\n  // Default: allow access if no specific restrictions\n  return true\n}\n\nfunction addSecurityHeaders(response: NextResponse): NextResponse {\n  // Security headers\n  response.headers.set('X-Content-Type-Options', 'nosniff')\n  response.headers.set('X-Frame-Options', 'DENY')\n  response.headers.set('X-XSS-Protection', '1; mode=block')\n  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')\n  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()')\n\n  // CSRF protection for state-changing requests\n  if (response.headers.get('content-type')?.includes('text/html')) {\n    response.headers.set('X-CSRF-Protection', '1')\n  }\n\n  return response\n}\n\nexport default auth((req) => {\n  const { pathname } = req.nextUrl\n  const isLoggedIn = !!req.auth\n  const user = req.auth?.user\n\n  // Public routes that don't require authentication\n  const publicRoutes = [\"/login\", \"/\", \"/api/auth/signin\", \"/api/auth/signout\"]\n  const isPublicRoute = publicRoutes.some(route => pathname === route || pathname.startsWith(route))\n\n  // Static files and API routes that should be excluded\n  const excludedPaths = [\n    \"/_next\",\n    \"/favicon.ico\",\n    \"/api/auth/signin\",\n    \"/api/auth/signout\",\n    \"/api/auth/session\",\n    \"/api/auth/csrf\"\n  ]\n  const isExcludedPath = excludedPaths.some(path => pathname.startsWith(path))\n\n  if (isExcludedPath) {\n    return addSecurityHeaders(NextResponse.next())\n  }\n\n  // If user is not logged in and trying to access protected route\n  if (!isLoggedIn && !isPublicRoute) {\n    if (pathname.startsWith('/api/')) {\n      return NextResponse.json(\n        { success: false, error: 'Authentication required' },\n        { status: 401 }\n      )\n    }\n    return NextResponse.redirect(new URL(\"/login\", req.url))\n  }\n\n  // If user is logged in and trying to access login page\n  if (isLoggedIn && pathname === \"/login\") {\n    return NextResponse.redirect(new URL(\"/dashboard\", req.url))\n  }\n\n  // Check if user account is active\n  if (isLoggedIn && user && !user.isActive) {\n    if (pathname.startsWith('/api/')) {\n      return NextResponse.json(\n        { success: false, error: 'Account is inactive' },\n        { status: 403 }\n      )\n    }\n    return NextResponse.redirect(new URL(\"/login?error=account_inactive\", req.url))\n  }\n\n  // Check if user must change password\n  if (isLoggedIn && user && user.mustChangePassword && pathname !== \"/change-password\" && !pathname.startsWith('/api/auth/profile')) {\n    if (pathname.startsWith('/api/')) {\n      return NextResponse.json(\n        { success: false, error: 'Password change required' },\n        { status: 403 }\n      )\n    }\n    return NextResponse.redirect(new URL(\"/change-password\", req.url))\n  }\n\n  // Role-based access control\n  if (isLoggedIn && user) {\n    const hasAccess = hasRouteAccess(pathname, user.role as UserRole, user.permissions)\n\n    if (!hasAccess) {\n      if (pathname.startsWith('/api/')) {\n        return NextResponse.json(\n          { success: false, error: 'Insufficient permissions' },\n          { status: 403 }\n        )\n      }\n      return NextResponse.redirect(new URL(\"/dashboard?error=access_denied\", req.url))\n    }\n  }\n\n  // Add security headers to response\n  return addSecurityHeaders(NextResponse.next())\n})\n\nexport const config = {\n  matcher: [\"/((?!_next/static|_next/image|favicon.ico).*)\"]\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;;;;AAEA,4BAA4B;AAC5B,MAAM,oBAAmF;IACvF,mBAAmB;IACnB,cAAc;QAAE,OAAO;YAAC,sJAAA,CAAA,WAAQ,CAAC,KAAK;YAAE,sJAAA,CAAA,WAAQ,CAAC,SAAS;YAAE,sJAAA,CAAA,WAAQ,CAAC,OAAO;YAAE,sJAAA,CAAA,WAAQ,CAAC,OAAO;SAAC;IAAC;IAEhG,qBAAqB;IACrB,aAAa;QAAE,OAAO;YAAC,sJAAA,CAAA,WAAQ,CAAC,KAAK;YAAE,sJAAA,CAAA,WAAQ,CAAC,SAAS;YAAE,sJAAA,CAAA,WAAQ,CAAC,OAAO;SAAC;IAAC;IAC7E,oBAAoB;QAAE,OAAO;YAAC,sJAAA,CAAA,WAAQ,CAAC,KAAK;YAAE,sJAAA,CAAA,WAAQ,CAAC,SAAS;SAAC;IAAC;IAClE,kBAAkB;QAAE,OAAO;YAAC,sJAAA,CAAA,WAAQ,CAAC,KAAK;YAAE,sJAAA,CAAA,WAAQ,CAAC,SAAS;SAAC;IAAC;IAEhE,aAAa;IACb,eAAe;QAAE,OAAO;YAAC,sJAAA,CAAA,WAAQ,CAAC,KAAK;YAAE,sJAAA,CAAA,WAAQ,CAAC,SAAS;YAAE,sJAAA,CAAA,WAAQ,CAAC,OAAO;SAAC;IAAC;IAC/E,YAAY;QAAE,OAAO;YAAC,sJAAA,CAAA,WAAQ,CAAC,KAAK;YAAE,sJAAA,CAAA,WAAQ,CAAC,SAAS;YAAE,sJAAA,CAAA,WAAQ,CAAC,OAAO;YAAE,sJAAA,CAAA,WAAQ,CAAC,OAAO;SAAC;IAAC;IAE9F,UAAU;IACV,YAAY;QAAE,OAAO;YAAC,sJAAA,CAAA,WAAQ,CAAC,KAAK;YAAE,sJAAA,CAAA,WAAQ,CAAC,SAAS;YAAE,sJAAA,CAAA,WAAQ,CAAC,OAAO;SAAC;IAAC;IAC5E,cAAc;QAAE,OAAO;YAAC,sJAAA,CAAA,WAAQ,CAAC,KAAK;YAAE,sJAAA,CAAA,WAAQ,CAAC,SAAS;SAAC;IAAC;IAE5D,kBAAkB;IAClB,UAAU;QAAE,OAAO;YAAC,sJAAA,CAAA,WAAQ,CAAC,KAAK;SAAC;IAAC;IACpC,aAAa;QAAE,OAAO;YAAC,sJAAA,CAAA,WAAQ,CAAC,KAAK;YAAE,sJAAA,CAAA,WAAQ,CAAC,SAAS;SAAC;IAAC;AAC7D;AAEA,wBAAwB;AACxB,MAAM,kBAAiF;IACrF,sBAAsB;IACtB,mBAAmB;QAAE,OAAO;YAAC,sJAAA,CAAA,WAAQ,CAAC,KAAK;SAAC;IAAC;IAC7C,qBAAqB;QAAE,OAAO;YAAC,sJAAA,CAAA,WAAQ,CAAC,KAAK;YAAE,sJAAA,CAAA,WAAQ,CAAC,SAAS;YAAE,sJAAA,CAAA,WAAQ,CAAC,OAAO;YAAE,sJAAA,CAAA,WAAQ,CAAC,OAAO;SAAC;IAAC;IAEvG,eAAe;IACf,iBAAiB;QAAE,OAAO;YAAC,sJAAA,CAAA,WAAQ,CAAC,KAAK;YAAE,sJAAA,CAAA,WAAQ,CAAC,SAAS;YAAE,sJAAA,CAAA,WAAQ,CAAC,OAAO;SAAC;IAAC;IAEjF,kBAAkB;IAClB,mBAAmB;QAAE,OAAO;YAAC,sJAAA,CAAA,WAAQ,CAAC,KAAK;YAAE,sJAAA,CAAA,WAAQ,CAAC,SAAS;YAAE,sJAAA,CAAA,WAAQ,CAAC,OAAO;SAAC;IAAC;IACnF,gBAAgB;QAAE,OAAO;YAAC,sJAAA,CAAA,WAAQ,CAAC,KAAK;YAAE,sJAAA,CAAA,WAAQ,CAAC,SAAS;YAAE,sJAAA,CAAA,WAAQ,CAAC,OAAO;YAAE,sJAAA,CAAA,WAAQ,CAAC,OAAO;SAAC;IAAC;IAElG,iBAAiB;IACjB,kBAAkB;QAAE,OAAO;YAAC,sJAAA,CAAA,WAAQ,CAAC,KAAK;YAAE,sJAAA,CAAA,WAAQ,CAAC,SAAS;SAAC;IAAC;IAEhE,eAAe;IACf,gBAAgB;QAAE,OAAO;YAAC,sJAAA,CAAA,WAAQ,CAAC,KAAK;YAAE,sJAAA,CAAA,WAAQ,CAAC,SAAS;YAAE,sJAAA,CAAA,WAAQ,CAAC,OAAO;SAAC;IAAC;AAClF;AAEA,SAAS,eAAe,QAAgB,EAAE,QAAkB,EAAE,eAA0B;IACtF,gCAAgC;IAChC,MAAM,aAAa,iBAAiB,CAAC,SAAS,IAAI,eAAe,CAAC,SAAS;IAC3E,IAAI,YAAY;QACd,OAAO,WAAW,KAAK,CAAC,QAAQ,CAAC;IACnC;IAEA,wBAAwB;IACxB,KAAK,MAAM,CAAC,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC;QAAE,GAAG,iBAAiB;QAAE,GAAG,eAAe;IAAC,GAAI;QAC1F,IAAI,SAAS,UAAU,CAAC,QAAQ;YAC9B,OAAO,OAAO,KAAK,CAAC,QAAQ,CAAC;QAC/B;IACF;IAEA,oDAAoD;IACpD,OAAO;AACT;AAEA,SAAS,mBAAmB,QAAsB;IAChD,mBAAmB;IACnB,SAAS,OAAO,CAAC,GAAG,CAAC,0BAA0B;IAC/C,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;IACxC,SAAS,OAAO,CAAC,GAAG,CAAC,oBAAoB;IACzC,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;IACxC,SAAS,OAAO,CAAC,GAAG,CAAC,sBAAsB;IAE3C,8CAA8C;IAC9C,IAAI,SAAS,OAAO,CAAC,GAAG,CAAC,iBAAiB,SAAS,cAAc;QAC/D,SAAS,OAAO,CAAC,GAAG,CAAC,qBAAqB;IAC5C;IAEA,OAAO;AACT;uCAEe,CAAA,GAAA,6HAAA,CAAA,OAAI,AAAD,EAAE,CAAC;IACnB,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,OAAO;IAChC,MAAM,aAAa,CAAC,CAAC,IAAI,IAAI;IAC7B,MAAM,OAAO,IAAI,IAAI,EAAE;IAEvB,kDAAkD;IAClD,MAAM,eAAe;QAAC;QAAU;QAAK;QAAoB;KAAoB;IAC7E,MAAM,gBAAgB,aAAa,IAAI,CAAC,CAAA,QAAS,aAAa,SAAS,SAAS,UAAU,CAAC;IAE3F,sDAAsD;IACtD,MAAM,gBAAgB;QACpB;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,iBAAiB,cAAc,IAAI,CAAC,CAAA,OAAQ,SAAS,UAAU,CAAC;IAEtE,IAAI,gBAAgB;QAClB,OAAO,mBAAmB,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC7C;IAEA,gEAAgE;IAChE,IAAI,CAAC,cAAc,CAAC,eAAe;QACjC,IAAI,SAAS,UAAU,CAAC,UAAU;YAChC,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAA0B,GACnD;gBAAE,QAAQ;YAAI;QAElB;QACA,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,IAAI,GAAG;IACxD;IAEA,uDAAuD;IACvD,IAAI,cAAc,aAAa,UAAU;QACvC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,cAAc,IAAI,GAAG;IAC5D;IAEA,kCAAkC;IAClC,IAAI,cAAc,QAAQ,CAAC,KAAK,QAAQ,EAAE;QACxC,IAAI,SAAS,UAAU,CAAC,UAAU;YAChC,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAsB,GAC/C;gBAAE,QAAQ;YAAI;QAElB;QACA,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,iCAAiC,IAAI,GAAG;IAC/E;IAEA,qCAAqC;IACrC,IAAI,cAAc,QAAQ,KAAK,kBAAkB,IAAI,aAAa,sBAAsB,CAAC,SAAS,UAAU,CAAC,sBAAsB;QACjI,IAAI,SAAS,UAAU,CAAC,UAAU;YAChC,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAA2B,GACpD;gBAAE,QAAQ;YAAI;QAElB;QACA,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,oBAAoB,IAAI,GAAG;IAClE;IAEA,4BAA4B;IAC5B,IAAI,cAAc,MAAM;QACtB,MAAM,YAAY,eAAe,UAAU,KAAK,IAAI,EAAc,KAAK,WAAW;QAElF,IAAI,CAAC,WAAW;YACd,IAAI,SAAS,UAAU,CAAC,UAAU;gBAChC,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;oBAAO,OAAO;gBAA2B,GACpD;oBAAE,QAAQ;gBAAI;YAElB;YACA,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,kCAAkC,IAAI,GAAG;QAChF;IACF;IAEA,mCAAmC;IACnC,OAAO,mBAAmB,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC7C;AAEO,MAAM,SAAS;IACpB,SAAS;QAAC;KAAgD;AAC5D"}}]}