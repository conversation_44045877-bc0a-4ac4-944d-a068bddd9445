// QRSAMS - QR Code Student Attendance Management System
// Comprehensive database schema for Philippine Grade 7-12 education system
// Optimized for DepEd compliance and local deployment with SQLite

generator client {
  provider = "prisma-client-js"
  output   = "../lib/generated/prisma"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// ============================================================================
// AUTHENTICATION MODELS
// ============================================================================

// User Model - System users with authentication and role-based access
model User {
  id           String @id @default(cuid())
  username     String @unique
  email        String @unique
  passwordHash String

  // Personal information
  firstName  String
  lastName   String
  middleName String?

  // Role and permissions
  role        UserRole @default(SCANNER)
  permissions String? // JSON array of specific permissions
  isActive    Boolean  @default(true)

  // Security fields
  lastLoginAt         DateTime?
  lastLoginIp         String?
  failedLoginAttempts Int       @default(0)
  lockedUntil         DateTime?
  passwordChangedAt   DateTime  @default(now())
  mustChangePassword  Boolean   @default(true)

  // Two-factor authentication
  twoFactorEnabled Boolean @default(false)
  twoFactorSecret  String?

  // Session management
  sessionToken     String?
  sessionExpiresAt DateTime?

  // Profile information
  phoneNumber String?
  department  String?
  position    String?

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  createdBy    String?
  creator      User?   @relation("UserCreator", fields: [createdBy], references: [id])
  createdUsers User[]  @relation("UserCreator")

  // Security relations
  loginHistory   LoginHistory[]
  securityEvents SecurityEvent[]
  auditLogs      AuditLog[]      @relation("UserAuditLogs")

  // Teacher relation (if user is a teacher)
  teacherId String?  @unique
  teacher   Teacher? @relation(fields: [teacherId], references: [id])

  // Indexes for performance
  @@index([email])
  @@index([username])
  @@index([role])
  @@index([isActive])
  @@index([lastLoginAt])
  @@map("users")
}

// LoginHistory Model - Track user login attempts and sessions
model LoginHistory {
  id     String @id @default(cuid())
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Login details
  loginAt   DateTime @default(now())
  ipAddress String
  userAgent String?
  location  String? // Geolocation if available

  // Login result
  success       Boolean
  failureReason String?

  // Session information
  sessionId       String?
  sessionDuration Int? // Duration in minutes
  logoutAt        DateTime?
  logoutReason    String? // manual, timeout, forced

  // Device information
  deviceType      String? // mobile, desktop, tablet
  browser         String?
  operatingSystem String?

  // Timestamps
  createdAt DateTime @default(now())

  // Indexes for performance
  @@index([userId, loginAt])
  @@index([success])
  @@index([ipAddress])
  @@map("login_history")
}

// SecurityEvent Model - Track security-related events
model SecurityEvent {
  id     String  @id @default(cuid())
  userId String?
  user   User?   @relation(fields: [userId], references: [id])

  // Event details
  eventType   SecurityEventType
  severity    SecuritySeverity  @default(LOW)
  description String

  // Context information
  ipAddress      String?
  userAgent      String?
  additionalData String? // JSON for extra context

  // Resolution
  resolved   Boolean   @default(false)
  resolvedAt DateTime?
  resolvedBy String?
  resolution String?

  // Timestamps
  createdAt DateTime @default(now())

  // Indexes for performance
  @@index([eventType])
  @@index([severity])
  @@index([userId, createdAt])
  @@index([resolved])
  @@map("security_events")
}

// Permission Model - Define granular permissions
model Permission {
  id          String @id @default(cuid())
  name        String @unique
  description String
  category    String // e.g., "students", "attendance", "reports"

  // Permission details
  resource String // What resource this permission applies to
  action   String // What action is allowed (create, read, update, delete)

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  rolePermissions RolePermission[]

  @@index([category])
  @@index([resource, action])
  @@map("permissions")
}

// RolePermission Model - Many-to-many relationship between roles and permissions
model RolePermission {
  id           String     @id @default(cuid())
  role         UserRole
  permissionId String
  permission   Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  // Permission constraints
  conditions String? // JSON for conditional permissions

  // Timestamps
  createdAt DateTime @default(now())

  @@unique([role, permissionId])
  @@map("role_permissions")
}

// ============================================================================
// CORE MODELS
// ============================================================================

// Student Model - Core student information with DepEd compliance
model Student {
  id          String     @id @default(cuid())
  studentId   String     @unique // DepEd format (e.g., 123456789012)
  firstName   String
  lastName    String
  middleName  String?
  email       String?
  dateOfBirth DateTime?
  gender      Gender?
  gradeLevel  GradeLevel
  section     String?
  course      String // Track/Strand (STEM, HUMSS, ABM, etc.)
  year        String // School year enrolled

  // Status and enrollment
  status         StudentStatus @default(ACTIVE)
  enrollmentDate DateTime      @default(now())

  // Guardian information
  guardianName         String
  guardianPhone        String
  guardianEmail        String?
  guardianRelationship GuardianRelationship

  // Emergency contact
  emergencyContactName         String
  emergencyContactPhone        String
  emergencyContactRelationship String?

  // Address (Tanauan, Leyte focus)
  address      String
  barangay     String?
  municipality String  @default("Tanauan")
  province     String  @default("Leyte")
  zipCode      String?

  // Media and QR
  photoUrl   String?
  qrCodeData String? // Generated QR code data

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  attendanceRecords  Attendance[]
  attendancePatterns AttendancePattern[]
  riskAssessments    StudentRiskAssessment[]
  smsLogs            SMSLog[]

  // Indexes for performance
  @@index([gradeLevel, section])
  @@index([status])
  @@index([guardianPhone])
  @@index([municipality, province])
  @@map("students")
}

// Teacher Model - Faculty and staff information
model Teacher {
  id          String  @id @default(cuid())
  employeeId  String  @unique
  firstName   String
  lastName    String
  middleName  String?
  email       String? @unique
  phoneNumber String?

  // Professional information
  role        TeacherRole @default(TEACHER)
  subjects    String? // JSON array of subject codes
  gradeLevels String? // JSON array of grade levels they teach
  sections    String? // JSON array of sections they handle

  // Employment details
  status   EmployeeStatus @default(ACTIVE)
  hireDate DateTime?

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  subjectsTeaching  Subject[]
  attendanceScanned Attendance[] @relation("ScannedByTeacher")
  user              User? // Optional user account for authentication

  // Indexes for performance
  @@index([role])
  @@index([status])
  @@map("teachers")
}

// Subject Model - Academic subjects and schedules
model Subject {
  id          String     @id @default(cuid())
  subjectCode String     @unique
  subjectName String
  gradeLevel  GradeLevel
  credits     Int        @default(1)
  room        String?

  // Schedule information (JSON format for flexibility)
  schedule String? // JSON: [{"day": "Monday", "startTime": "08:00", "endTime": "09:00"}]

  // Teacher assignment
  teacherId String?
  teacher   Teacher? @relation(fields: [teacherId], references: [id])

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  attendanceRecords Attendance[]

  // Indexes for performance
  @@index([gradeLevel])
  @@index([teacherId])
  @@map("subjects")
}

// Attendance Model - Core attendance tracking
model Attendance {
  id        String  @id @default(cuid())
  studentId String
  student   Student @relation(fields: [studentId], references: [id], onDelete: Cascade)

  // Date and time information
  date    DateTime // Date of attendance (YYYY-MM-DD)
  timeIn  DateTime?
  timeOut DateTime?

  // Attendance details
  attendanceType AttendanceType   @default(GATE)
  subjectId      String?
  subject        Subject?         @relation(fields: [subjectId], references: [id])
  status         AttendanceStatus @default(PRESENT)

  // Tracking information
  scannedBy        String? // Teacher ID who scanned
  scannedByTeacher Teacher? @relation("ScannedByTeacher", fields: [scannedBy], references: [id])
  notes            String?
  isManualEntry    Boolean  @default(false)

  // Location and device info
  location String? // Gate, Classroom, etc.
  deviceId String? // Scanner device identifier

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Indexes for performance
  @@unique([studentId, date, attendanceType, subjectId])
  @@index([date])
  @@index([studentId, date])
  @@index([status])
  @@index([attendanceType])
  @@map("attendance")
}

// ============================================================================
// ANALYTICS AND AI MODELS
// ============================================================================

// AttendancePattern Model - AI analytics for attendance patterns
model AttendancePattern {
  id        String  @id @default(cuid())
  studentId String
  student   Student @relation(fields: [studentId], references: [id], onDelete: Cascade)

  // Pattern metrics
  weeklyRate          Float // Weekly attendance rate (0-1)
  monthlyRate         Float // Monthly attendance rate (0-1)
  consecutiveAbsences Int     @default(0)
  latePattern         String? // JSON: frequency of late arrivals by day/time

  // Risk assessment
  riskScore Float     @default(0) // 0-100 risk score
  riskLevel RiskLevel @default(LOW)

  // AI predictions and insights
  predictions String? // JSON: AI predictions for future attendance
  insights    String? // JSON: AI-generated insights

  // Analysis metadata
  lastAnalyzed DateTime @default(now())
  dataPoints   Int      @default(0) // Number of attendance records analyzed

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([studentId])
  @@map("attendance_patterns")
}

// StudentRiskAssessment Model - Comprehensive risk evaluation
model StudentRiskAssessment {
  id        String  @id @default(cuid())
  studentId String
  student   Student @relation(fields: [studentId], references: [id], onDelete: Cascade)

  // Risk metrics
  riskScore          Float // 0-100 overall risk score
  riskLevel          RiskLevel
  dropoutProbability Float // 0-1 probability of dropout

  // Risk factors (JSON format for flexibility)
  riskFactors   String? // JSON array of risk factors
  interventions String? // JSON array of recommended interventions

  // Parent engagement metrics
  parentEngagementScore Float     @default(50) // 0-100 engagement score
  lastParentContact     DateTime?
  parentResponseRate    Float     @default(0) // 0-1 response rate to communications

  // Assessment metadata
  assessmentDate DateTime @default(now())
  nextReviewDate DateTime
  assessedBy     String? // Teacher/Admin who conducted assessment

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("student_risk_assessments")
}

// SMSLog Model - SMS communication tracking
model SMSLog {
  id             String @id @default(cuid())
  recipientPhone String
  message        String

  // SMS status tracking
  status       SMSStatus @default(PENDING)
  sentAt       DateTime?
  deliveredAt  DateTime?
  errorMessage String?

  // Related information
  relatedStudentId String?
  relatedStudent   Student?    @relation(fields: [relatedStudentId], references: [id])
  messageType      MessageType @default(GENERAL)

  // Metadata
  provider  String? // SMS provider used
  messageId String? // Provider's message ID
  cost      Float? // Cost in PHP

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Indexes for performance
  @@index([status])
  @@index([relatedStudentId])
  @@index([messageType])
  @@index([sentAt])
  @@map("sms_logs")
}

// ============================================================================
// ADDITIONAL ANALYTICS MODELS
// ============================================================================

// SystemSettings Model - Application configuration
model SystemSettings {
  id          String  @id @default(cuid())
  key         String  @unique
  value       String
  description String?
  category    String  @default("general")

  // Metadata
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("system_settings")
}

// AuditLog Model - System activity tracking
model AuditLog {
  id         String  @id @default(cuid())
  userId     String? // User who performed action
  user       User?   @relation("UserAuditLogs", fields: [userId], references: [id])
  action     String // Action performed
  entityType String // Student, Teacher, Attendance, etc.
  entityId   String? // ID of affected entity

  // Change details
  oldValues String? // JSON of old values
  newValues String? // JSON of new values
  ipAddress String?
  userAgent String?

  // Timestamps
  createdAt DateTime @default(now())

  // Indexes for performance
  @@index([userId])
  @@index([action])
  @@index([entityType])
  @@index([createdAt])
  @@map("audit_logs")
}

// NotificationQueue Model - Queued notifications
model NotificationQueue {
  id            String @id @default(cuid())
  recipientId   String // Student ID or Teacher ID
  recipientType String // "student" or "teacher"

  // Notification details
  type     MessageType
  title    String
  message  String
  priority NotificationPriority @default(NORMAL)

  // Delivery channels
  channels String // JSON array: ["sms", "email", "push"]

  // Status tracking
  status        NotificationStatus @default(PENDING)
  scheduledFor  DateTime?
  sentAt        DateTime?
  deliveredAt   DateTime?
  failureReason String?
  retryCount    Int                @default(0)
  maxRetries    Int                @default(3)

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Indexes for performance
  @@index([status])
  @@index([scheduledFor])
  @@index([recipientId, recipientType])
  @@map("notification_queue")
}

// ============================================================================
// ENUMS
// ============================================================================

// Authentication-related enums
enum UserRole {
  ADMIN // Full system access
  PRINCIPAL // School administration access
  TEACHER // Classroom and student management
  SCANNER // QR scanning and basic attendance only
}

enum SecurityEventType {
  LOGIN_SUCCESS
  LOGIN_FAILURE
  LOGOUT
  PASSWORD_CHANGE
  ACCOUNT_LOCKED
  ACCOUNT_UNLOCKED
  PERMISSION_DENIED
  SUSPICIOUS_ACTIVITY
  DATA_EXPORT
  BULK_OPERATION
  SYSTEM_ACCESS
}

enum SecuritySeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

// Student-related enums
enum Gender {
  MALE
  FEMALE
}

enum GradeLevel {
  GRADE_7
  GRADE_8
  GRADE_9
  GRADE_10
  GRADE_11
  GRADE_12
}

enum StudentStatus {
  ACTIVE
  INACTIVE
  TRANSFERRED
  GRADUATED
  DROPPED
}

enum GuardianRelationship {
  FATHER
  MOTHER
  GUARDIAN
  GRANDPARENT
  SIBLING
  AUNT
  UNCLE
  OTHER
}

// Teacher-related enums
enum TeacherRole {
  TEACHER
  DEPARTMENT_HEAD
  GUIDANCE_COUNSELOR
  ADMIN
  PRINCIPAL
  VICE_PRINCIPAL
}

enum EmployeeStatus {
  ACTIVE
  INACTIVE
  RESIGNED
  TERMINATED
  RETIRED
}

// Attendance-related enums
enum AttendanceType {
  GATE // School gate entry/exit
  SUBJECT // Subject-specific attendance
  EVENT // Special events
  ASSEMBLY // School assemblies
}

enum AttendanceStatus {
  PRESENT
  ABSENT
  LATE
  EXCUSED
  HALF_DAY
}

// Analytics and risk-related enums
enum RiskLevel {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

// SMS-related enums
enum SMSStatus {
  PENDING
  SENT
  DELIVERED
  FAILED
  EXPIRED
}

enum MessageType {
  ATTENDANCE_ALERT // Absence notifications
  LATE_ARRIVAL // Late arrival notifications
  RISK_WARNING // Risk assessment alerts
  GENERAL // General announcements
  EMERGENCY // Emergency notifications
  PARENT_MEETING // Meeting invitations
  ACADEMIC_UPDATE // Academic performance updates
}

// Notification-related enums
enum NotificationPriority {
  LOW
  NORMAL
  HIGH
  URGENT
}

enum NotificationStatus {
  PENDING
  SCHEDULED
  SENT
  DELIVERED
  FAILED
  CANCELLED
}
